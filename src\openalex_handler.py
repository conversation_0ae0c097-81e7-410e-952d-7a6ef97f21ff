import requests
import time
import json
from itertools import cycle
import os
import sqlite3
import argparse
import sys
from datetime import datetime
import textwrap
import logging
from typing import Literal, List, Dict
import hashlib
from abc import ABC, abstractmethod


# interested_fields = [ "ids", "title", "abstract_inverted_index", "indexed_in", "authorships", "cited_by_count", "concepts", "fwci", "is_paratext", "is_retracted", "keywords", "language", "license", "locations", "open_access", "primary_location", "publication_date", "publication_year", "topics", "type", "type_crossref" ]
class DatabaseHandler(ABC):
    """Abstract base class for database operations."""
    
    @abstractmethod
    def _ensure_db_directory(self):
        """Ensure the database directory exists."""
        pass
    
    @abstractmethod
    def _init_db(self):
        """Initialize database and tables."""
        pass
    
    @abstractmethod
    def _create_tables(self):
        """Create necessary database tables if they don't exist."""
        pass
    
    @abstractmethod
    def _log_table_stats(self):
        """Log statistics about existing tables."""
        pass
    
    @abstractmethod
    def upsert_work(self, work_data):
        """Insert or update a work record."""
        pass
    
    @abstractmethod
    def upsert_checkpoint(self, checkpoint_data):
        """Insert or update a checkpoint record."""
        pass
    
    @abstractmethod
    def get_checkpoint(self, query_hash):
        """Get a checkpoint by query hash."""
        pass
    
    @abstractmethod
    def get_work_count(self):
        """Get the total number of works in the database."""
        pass
    
    @abstractmethod
    def close(self):
        """Close the database connection."""
        pass

class SQLiteHandler(DatabaseHandler):
    """SQLite implementation of DatabaseHandler."""
    
    def __init__(self, db_path):
        """Initialize database connection."""
        logging.debug(f"Initializing SQLiteHandler with path: {db_path}")
        self.db_path = db_path
        self._ensure_db_directory()
        self._init_db()
        
    def _ensure_db_directory(self):
        """Ensure the database directory exists."""
        db_dir = os.path.dirname(self.db_path)
        if db_dir:
            os.makedirs(db_dir, exist_ok=True)
            logging.debug(f"Ensured database directory exists: {db_dir}")
    
    def _init_db(self):
        """Initialize database and tables."""
        try:
            self.db_exists = os.path.exists(self.db_path)
            logging.debug(f"Database exists: {self.db_exists}")
            
            # Create connection with row factory
            self.conn = sqlite3.connect(self.db_path)
            self.conn.row_factory = sqlite3.Row  # This enables column access by name: row['column_name']
            
            # Create tables if they don't exist
            self._create_tables()
            
            if not self.db_exists:
                logging.info(f"Created new database at {self.db_path}")
            else:
                logging.info(f"Connected to existing database at {self.db_path}")
                self._log_table_stats()
                
        except Exception as e:
            logging.error(f"Error initializing database: {e}")
            raise
    
    def _create_tables(self):
        """Create necessary database tables if they don't exist."""
        try:
            with self.conn:
                cursor = self.conn.cursor()
                # Create works table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS dim_openalex_works (
                        id TEXT PRIMARY KEY,
                        title TEXT,
                        abstract TEXT,
                        ids TEXT,
                        indexed_in TEXT,
                        authorships TEXT,
                        cited_by_count INTEGER,
                        concepts TEXT,
                        fwci REAL,
                        is_paratext BOOLEAN,
                        is_retracted BOOLEAN,
                        keywords TEXT,
                        language TEXT,
                        license TEXT,
                        locations TEXT,
                        open_access TEXT,
                        primary_location TEXT,
                        publication_date TEXT,
                        publication_year INTEGER,
                        topics TEXT,
                        type TEXT,
                        type_crossref TEXT,
                        created_at TEXT
                    )
                ''')
                
                # Create checkpoint table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS dim_openalex_checkpoint (
                        query_hash TEXT PRIMARY KEY,
                        next_cursor TEXT,
                        total_collected INTEGER,
                        total_count INTEGER,
                        query_params TEXT,
                        status TEXT,
                        last_updated TEXT,
                        completion_percentage REAL
                    )
                ''')
                logging.debug("Database tables created successfully")
                
        except Exception as e:
            logging.error(f"Error creating tables: {e}")
            raise
    
    def _log_table_stats(self):
        """Log statistics about existing tables."""
        try:
            tables = ['dim_openalex_works', 'dim_openalex_checkpoint']
            with self.conn:
                cursor = self.conn.cursor()
                for table in tables:
                    cursor.execute(f"SELECT COUNT(*) as count FROM {table}")
                    count = cursor.fetchone()[0]
                    logging.info(f"Total records in {table} table: {count}")
                    
        except Exception as e:
            logging.error(f"Error getting table stats: {e}")
    
    def upsert_work(self, work_data):
        """Insert or update a work record."""
        try:
            with self.conn:
                cursor = self.conn.cursor()
                # Extract fields from work_data
                work = {
                    'id': work_data.get('id'),
                    'title': work_data.get('title'),
                    'abstract': work_data.get('abstract'),
                    'ids': json.dumps(work_data.get('ids')),
                    'indexed_in': json.dumps(work_data.get('indexed_in')),
                    'authorships': json.dumps(work_data.get('authorships')),
                    'cited_by_count': work_data.get('cited_by_count'),
                    'concepts': json.dumps(work_data.get('concepts')),
                    'fwci': work_data.get('fwci'),
                    'is_paratext': work_data.get('is_paratext'),
                    'is_retracted': work_data.get('is_retracted'),
                    'keywords': json.dumps(work_data.get('keywords')),
                    'language': work_data.get('language'),
                    'license': json.dumps(work_data.get('license')),
                    'locations': json.dumps(work_data.get('locations')),
                    'open_access': json.dumps(work_data.get('open_access')),
                    'primary_location': json.dumps(work_data.get('primary_location')),
                    'publication_date': work_data.get('publication_date'),
                    'publication_year': work_data.get('publication_year'),
                    'topics': json.dumps(work_data.get('topics')),
                    'type': work_data.get('type'),
                    'type_crossref': work_data.get('type_crossref'),
                    'created_at': datetime.now().isoformat()
                }
                
                # Prepare the upsert query
                placeholders = ', '.join(['?' for _ in work.keys()])
                columns = ', '.join(work.keys())
                update_stmt = ', '.join([f"{k}=excluded.{k}" for k in work.keys()])
                
                query = f'''
                    INSERT INTO dim_openalex_works ({columns})
                    VALUES ({placeholders})
                    ON CONFLICT(id) DO UPDATE SET
                    {update_stmt}
                '''
                
                cursor.execute(query, list(work.values()))
                logging.debug(f"Upserted work record with ID: {work['id']}")
            
        except Exception as e:
            logging.error(f"Error upserting work: {e}")
            raise
    
    def upsert_checkpoint(self, checkpoint_data):
        """Insert or update a checkpoint record."""
        try:
            with self.conn:
                cursor = self.conn.cursor()
                checkpoint = {
                    'query_hash': checkpoint_data['query_hash'],
                    'next_cursor': checkpoint_data['next_cursor'],
                    'total_collected': checkpoint_data['total_collected'],
                    'total_count': checkpoint_data['total_count'],
                    'query_params': json.dumps(checkpoint_data['query_params']),
                    'status': checkpoint_data['status'],
                    'last_updated': checkpoint_data['last_updated'],
                    'completion_percentage': checkpoint_data['completion_percentage']
                }
                
                placeholders = ', '.join(['?' for _ in checkpoint.keys()])
                columns = ', '.join(checkpoint.keys())
                update_stmt = ', '.join([f"{k}=excluded.{k}" for k in checkpoint.keys()])
                
                query = f'''
                    INSERT INTO dim_openalex_checkpoint ({columns})
                    VALUES ({placeholders})
                    ON CONFLICT(query_hash) DO UPDATE SET
                    {update_stmt}
                '''
                
                cursor.execute(query, list(checkpoint.values()))
                logging.debug(f"Upserted checkpoint with hash: {checkpoint['query_hash']}")
            
        except Exception as e:
            logging.error(f"Error upserting checkpoint: {e}")
            raise
    
    def get_checkpoint(self, query_hash):
        """Get a checkpoint by query hash."""
        try:
            with self.conn:
                cursor = self.conn.cursor()
                cursor.execute(
                    "SELECT * FROM dim_openalex_checkpoint WHERE query_hash = ?",
                    (query_hash,)
                )
                row = cursor.fetchone()
                if row:
                    checkpoint = dict(row)
                    checkpoint['query_params'] = json.loads(checkpoint['query_params'])
                    logging.debug(f"Retrieved checkpoint: {checkpoint}")
                    return checkpoint
                return None
            
        except Exception as e:
            logging.error(f"Error getting checkpoint: {e}")
            raise
    
    def get_work_count(self):
        """Get the total number of works in the database."""
        try:
            with self.conn:
                cursor = self.conn.cursor()
                cursor.execute("SELECT COUNT(*) as count FROM dim_openalex_works")
                count = cursor.fetchone()[0]
                logging.debug(f"Total works in database: {count}")
                return count
        except Exception as e:
            logging.error(f"Error getting work count: {e}")
            raise
    
    def close(self):
        """Close the database connection."""
        try:
            if hasattr(self, 'conn'):
                self.conn.close()
                logging.debug("Database connection closed")
        except Exception as e:
            logging.error(f"Error closing database connection: {e}")

class OpenAlexClient:
    """A client for interacting with the OpenAlex API with support for rate limiting and data persistence."""
    
    def __init__(self, email_addresses: List[str], max_requests_per_email: int = 90000, checkpoint_frequency: int = 1000, base_url: str = "https://api.openalex.org", db_path: str = "openalex_data.db", logger:logging.Logger=None):
        """
        Initialize the OpenAlex client.
        
        Args:
            email_addresses (list): List of email addresses to use for the polite pool
            max_requests_per_email (int): Maximum number of requests per email
            checkpoint_frequency (int): Frequency of checkpointing the progress of the download
            base_url (str): Base URL for the OpenAlex API
            db_path (str): Path to database file
            logger (logging.Logger): Logger instance
        """
        self.logger = logger
        self.logger.debug("Initializing OpenAlex client")

        self.base_url = base_url
        self.email_addresses = cycle(email_addresses)
        self.current_email = next(self.email_addresses)
        self.requests_count = 0
        self.max_requests_per_email = max_requests_per_email
        self.checkpoint_frequency = checkpoint_frequency
        
        self.logger.debug(f"Base URL: {base_url}")
        self.logger.debug(f"Initial email: {self.current_email}")
        self.logger.debug(f"Max requests per email: {self.max_requests_per_email}")
        
        # Initialize database handler
        if db_path.endswith(".db"):
            self.db = SQLiteHandler(db_path)
        else:
            raise ValueError(f"Unsupported database type: {db_path}")

    def _rotate_email(self):
        """Rotate to the next email address."""
        old_email = self.current_email
        self.current_email = next(self.email_addresses)
        self.requests_count = 0
        self.logger.info(f"Rotating email from {old_email} to {self.current_email}")

    def _make_request(self, url, params):
        """Make a request with error handling and email rotation."""
        params['mailto'] = self.current_email
        
        while True:
            try:
                self.logger.debug(f"Making request to {url} with params: {params}")
                response = requests.get(url, params=params)
                self.requests_count += 1
                self.logger.debug(f"Request count for current email: {self.requests_count}")

                if self.requests_count >= self.max_requests_per_email:
                    self.logger.info(f"Reached max requests ({self.max_requests_per_email}) for current email")
                    self._rotate_email()
                
                if response.status_code == 429:
                    self.logger.warning("Rate limit hit, waiting 60 seconds...")
                    time.sleep(60)
                    continue
                    
                response.raise_for_status()
                self.logger.debug(f"Request successful: {response.status_code}")
                return response.json()

            except requests.exceptions.RequestException as e:
                self.logger.error(f"Request error: {e}")
                self.logger.debug("Retrying in 10 seconds...")
                time.sleep(10)
                continue

    def _get_query_hash(self, query_params):
        """Generate a unique hash for a query to track its completion status."""
        query_str = f"works_{json.dumps(query_params, sort_keys=True)}"
        query_hash = hashlib.sha256(query_str.encode()).hexdigest()
        self.logger.debug(f"Generated query hash: {query_hash}")
        return query_hash

    def _save_checkpoint(self, query_hash, next_cursor, total_collected, total_count, query_params, status='in_progress'):
        """Save progress to database checkpoint with query information."""
        checkpoint_data = {
            'query_hash': query_hash,
            'next_cursor': next_cursor,
            'total_collected': total_collected,
            'total_count': total_count,
            'query_params': json.dumps(query_params),
            'status': status,
            'last_updated': datetime.now().isoformat(),
            'completion_percentage': (total_collected / total_count * 100) if total_count > 0 else 0
        }
        
        self.db.upsert_checkpoint(checkpoint_data)
        self.logger.info(f"Checkpoint saved: {total_collected}/{total_count} results ({checkpoint_data['completion_percentage']:.2f}%)")
        self.logger.debug(f"Checkpoint details: {checkpoint_data}")

    def _load_checkpoint(self, query_hash):
        """Load the most recent progress for a specific query."""
        checkpoint = self.db.get_checkpoint(query_hash)
        if checkpoint is not None:
            self.logger.info(f"Resuming from checkpoint: {checkpoint['total_collected']}/{checkpoint['total_count']} results ({checkpoint['completion_percentage']:.2f}%)")
            self.logger.debug(f"Checkpoint details: {checkpoint}")
            return checkpoint['next_cursor'], checkpoint['total_collected']
        self.logger.debug(f"No checkpoint found for query hash: {query_hash}")
        return "*", 0

    def _is_query_completed(self, query_hash):
        """Check if a query has been completed previously."""
        checkpoint = self.db.get_checkpoint(query_hash)
        is_completed = checkpoint is not None and checkpoint.get('status') == 'completed'
        self.logger.debug(f"Query completion status for {query_hash}: {is_completed}")
        return is_completed

    def _save_works(self, works):
        """Save works to the database."""
        saved_count = 0
        for work in works:
            work_id = work.get('id')
            if work_id:
                if work.get('abstract_inverted_index'):
                    work['abstract'] = self._reconstruct_abstract(work.get('abstract_inverted_index'))
                else:
                    work['abstract'] = None
                work['retrieved_date'] = datetime.now().isoformat()
                work.pop('abstract_inverted_index')
                self.db.upsert_work(work)
                saved_count += 1
        self.logger.debug(f"Saved {saved_count} works to database")
    
    def _reconstruct_abstract(self, inverted_index:Dict[str, List[int]]):
        # Determine the length of the abstract by finding the highest index position
        max_index = max(position for positions in inverted_index.values() for position in positions)
        
        # Create a list to store the words in their original order
        abstract = [''] * (max_index + 1)
        
        for word, positions in inverted_index.items():
            for position in positions:
                abstract[position] = word
        
        # Join the list into a single string to form the abstract
        return ' '.join(abstract)

    def get_work(self, work_id):
        """
        Retrieve a single work by ID.
        
        Args:
            work_id (str): OpenAlex ID of the work
        """
        self.logger.debug(f"Retrieving work with ID: {work_id}")
        url = f"{self.base_url}/works/{work_id}"
        params = {'mailto': self.current_email}
        return self._make_request(url, params)

    def search_works(self, query_params):
        """
        Search for works using query parameters.
        
        Args:
            query_params (dict): Query parameters for the search
        """
        self.logger.debug(f"Searching works with params: {query_params}")
        url = f"{self.base_url}/works"
        return self._make_request(url, query_params)

    def get_works_count(self, query_params):
        """
        Get the total count of works matching the query.
        
        Args:
            query_params (dict): Query parameters for the search
        """
        self.logger.info(f"Getting count for works with params: {query_params}")
        data = self.search_works(query_params)
        if data:
            count = data.get("meta", {}).get("count", 0)
            self.logger.info(f"Found {count} matching works")
            return count
        self.logger.warning("Failed to get works count")
        return None

    def download_works(self, query_params):
        """
        Download all works matching the query parameters with automatic resume capability.
        
        Args:
            query_params (dict): Query parameters for the search
        """
        self.logger.debug("Starting download of works")
        self.logger.debug(f"Download parameters: {query_params}")
        
        query_hash = self._get_query_hash(query_params)
        
        # Check if query was already completed
        if self._is_query_completed(query_hash):
            checkpoint = self.db.get_checkpoint(query_hash)
            self.logger.info(f"Query already completed with {checkpoint['total_collected']} works available in database with completion date: {checkpoint['last_updated']} and completion percentage: {checkpoint['completion_percentage']:.2f}%")
            return True

        # Get total count for progress tracking
        total_count = self.get_works_count(query_params)
        if total_count is None:
            self.logger.error("Failed to get total count from OpenAlex API")
            return False

        # Load last checkpoint if exists
        next_cursor, total_collected = self._load_checkpoint(query_hash)
        
        try:
            while next_cursor:
                query_params["cursor"] = next_cursor
                
                # Measure API request time
                start_api_time = time.time()
                data = self.search_works(query_params)
                api_time = time.time() - start_api_time
                
                if not data:
                    self.logger.warning("No data received from API")
                    break
                
                results = data.get("results", [])
                if not results:
                    self.logger.warning("No results in API response")
                    break

                # Measure time to save works
                start_save_time = time.time()
                self._save_works(results)
                save_time = time.time() - start_save_time
                
                total_collected += len(results)
                
                next_cursor = data.get("meta", {}).get("next_cursor")
                
                self.logger.info(f"Retrieved {len(results)} works. Progress: {total_collected}/{total_count} ({(total_collected/total_count*100):.2f}%) | API Time: {api_time:.2f}s | Save Time: {save_time:.2f}s")
                
                if total_collected % self.checkpoint_frequency == 0:
                    self._save_checkpoint(query_hash, next_cursor, total_collected, total_count, query_params, status='in_progress')
                
                time.sleep(0.001)  # Polite delay between requests
            
            # Mark query as completed
            self._save_checkpoint(query_hash, None, total_collected, total_count, query_params, status='completed')
            self.logger.info(f"Download completed. Total works saved: {total_collected}")
            return True

        except Exception as e:
            self.logger.error(f"Error during download: {str(e)}")
            self.logger.debug("Error details:", exc_info=True)
            self._save_checkpoint(query_hash, next_cursor, total_collected, total_count, query_params)
            return False

def load_query_from_file(query_file:str, logger:logging.Logger):
    """Load query from a JSON file and extract email addresses."""
    try:
        with open(query_file, 'r') as f:
            data = json.load(f)
            if 'emails' not in data:
                logger.error("Error: Query file must contain 'emails' key with list of email addresses")
                sys.exit(1)
            emails = data.pop('emails')  # Remove emails from query params
            if not isinstance(emails, list) or not emails:
                logger.error("Error: 'emails' must be a non-empty list")
                sys.exit(1)
            return data, emails
    except json.JSONDecodeError:
        logger.error(f"Error: {query_file} contains invalid JSON")
        sys.exit(1)
    except FileNotFoundError:
        logger.error(f"Error: Query file {query_file} not found")
        sys.exit(1)

def setup_logging(logs_dir:str="logs", logging_level:Literal['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'] = 'WARNING'):
    """Configure logging based on verbosity level."""
    # Create logs directory if it doesn't exist
    os.makedirs(logs_dir, exist_ok=True)

    level = getattr(logging, logging_level)

    # Create formatters
    console_formatter = logging.Formatter('%(levelname)s - %(message)s')
    file_formatter = logging.Formatter('%(asctime)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s',
                                     datefmt='%Y-%m-%d %H:%M:%S')
    
    # Setup handlers
    console_handler = logging.StreamHandler()
    console_handler.setLevel(level)
    console_handler.setFormatter(console_formatter)
    
    log_file = f'{logs_dir}/openalex_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'
    file_handler = logging.FileHandler(log_file)
    file_handler.setLevel(logging.DEBUG)
    file_handler.setFormatter(file_formatter)
    
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.DEBUG)
    root_logger.handlers = []
    root_logger.addHandler(console_handler)
    root_logger.addHandler(file_handler)
    root_logger.info(f"Logging initialized. Log file: {log_file}")
    return root_logger

if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description='OpenAlex API Client Tool',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=textwrap.dedent('''
            Examples:
              # Get count of works matching query
              %(prog)s count --query-file queries/my_query.json
              
              # Download works and save to database
              %(prog)s download --query-file queries/works_query.json
        ''')
    )
    
    # Add arguments
    parser.add_argument('command', choices=['count', 'download'], help='Command to execute')
    parser.add_argument('--query-file', '-qf', type=str, required=True,
                       help='Path to JSON file containing query parameters and email addresses')
    parser.add_argument('--db-path', '-db', type=str, default='db/openalex_data.db',
                       help='Path to SQLite database file')
    parser.add_argument('--logs-dir', '-ld', type=str, default='logs',
                       help='Path to logs directory')
    parser.add_argument('--logging-level', '-ll', type=str, default='INFO',
                       help='Set logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)')    
    args = parser.parse_args()
    
    # Setup logging and configuration
    logger = setup_logging(logs_dir=args.logs_dir, logging_level=args.logging_level)
    logger.info("Starting OpenAlex client tool")
    
    # Load configuration
    query_params, email_addresses = load_query_from_file(args.query_file, logger)
    
    # Initialize client
    oaclient = OpenAlexClient(email_addresses=email_addresses, db_path=args.db_path, logger=logger)
        
    # Execute command
    if args.command == 'count':
        count = oaclient.get_works_count(query_params)
        if count is not None:
            logger.info(f"Total works found for given query: {count}")
        else:
            logger.error("Could not retrieve works count")
            
    elif args.command == 'download':
        count = oaclient.get_works_count(query_params)
        if count is not None:
            logger.info(f"Total works to download for given query: {count}")
            success = oaclient.download_works(query_params)
            if success:
                logger.info(f"Successfully downloaded works to database: {args.db_path}")
        else:
            logger.error("Failed to complete the works download operation")
        
    if hasattr(oaclient, 'db'):
        oaclient.db.close()
