"""
Pydantic data models for the ADC evaluation system.

This module defines all data structures used for ground truth, extraction results,
and evaluation responses with proper validation and type safety.

TEST FILE: tests/test_models.py
IMPORTANT: When modifying this file, ensure corresponding test cases in the test file
are updated or added to maintain test coverage and validate the changes.
"""

from pydantic import BaseModel
from typing import List, Optional, Any, Union

# Supporting Models for Endpoints
class GTDose(BaseModel):
    value: float
    unit: str
    
class GTTimePoint(BaseModel):
    value: float  
    unit: str

# Simplified Ground Truth Model - ENDPOINTS ONLY
class GTEndpoint(BaseModel):
    paper_id: str
    adc_name: str                    # Contains ADC information
    model_name: str                  # Contains Model information
    model_type: Optional[str] = None
    experiment_type: Optional[str] = None
    endpoint_name: str
    endpoint_type: Optional[str] = None
    endpoint_value: Optional[Union[float, int, str]] = None    # Can be float, int, or string (e.g., "High", "Low")
    endpoint_units: Optional[str] = None    # Allow None for endpoints without units
    endpoint_timepoint: Optional[str] = None
    endpoint_concentration: Optional[str] = None
    gt_id: Optional[str] = None      # Standardized GT ID for tracking (e.g., "GT_ROW_1")
    
class GroundTruthDocument(BaseModel):
    paper_id: str
    endpoints: List[GTEndpoint]  # ONLY endpoints - contains all ADC+Model+Endpoint info
    
# Simplified Extraction Models - ENDPOINTS ONLY
class ExtractedValueObject(BaseModel):
    value: Union[float, int, str]
    unit: Optional[str] = None
    citation: str
    
class ExtractedDose(BaseModel):
    value: Union[float, int, str]
    unit: Optional[str] = None
    citation: str
    
class Measurement(BaseModel):
    experiment_type: Optional[str] = None
    model_type: Optional[str] = None
    citations: Optional[List[str]] = None
    measured_value: Optional[str] = None
    measured_time: Optional[str] = None
    measured_concentration: Optional[str] = None

    
class ExtractedEndpoint(BaseModel):
    paper_id: Optional[str] = None
    type: Optional[str] = None
    adc_name: str
    model_name: str
    model_type: Optional[str] = None
    experiment_type: Optional[str] = None
    endpoint_name: str
    endpoint_type: Optional[str] = None
    comments: Optional[str] = None
    source: Optional[str] = None
    status: Optional[str] = None
    status_cat: Optional[str] = None
    endpoint_measurements: Optional[List[Measurement]] = None
    
# Simplified - only ExtractedEndpoint now
ExtractionRow = ExtractedEndpoint

# LLM Response Model
class LLMMatchResponse(BaseModel):
    matched: bool
    gt_id: Optional[str] = None  # For identifying which GT item matched
    matched_gt_endpoint: Optional["GTEndpoint"] = None  # The actual matched GT endpoint for removal
    confidence: float  # 0.0 to 1.0
    reason: str
    reasoning: Optional[str] = None  # The LLM's detailed reasoning process
    reasoning_tokens: Optional[int] = None  # Number of reasoning tokens used
    language_model: Optional[str] = None  # Which model was used (o3-mini, gpt-4.1, etc.)
    
# Individual Match Result
class MatchResult(BaseModel):
    extraction_row: dict  # The original extraction row data
    llm_response: LLMMatchResponse  # The LLM's match response
    classification: str  # "TP", "FP", or "FN"
    extraction_row_index: int  # Index in the original extraction list
    comments: Optional[str] = None
    source: Optional[str] = None
    status: Optional[str] = None
    status_cat: Optional[str] = None

# Evaluation Results
class EvaluationResult(BaseModel):
    paper_id: str
    TP: int
    FP: int  
    FN: int
    precision: float
    recall: float
    f1: float
    llm_vs_human_corr_overall: Optional[float] = None
    llm_vs_human_corr_by_endpoint: Optional[dict] = None
    detailed_results: List[MatchResult] = []  # Detailed results for each extraction row
    unmatched_gt_items: List[dict] = []  # GT items that weren't matched (contributing to FN)