import json
import pandas as pd
import os
import argparse
from pathlib import Path

def process_json_file(file_path):
    """Process a single JSON file and extract ADC, model, and endpoint data."""
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    file_id = Path(file_path).stem.split('_results')[0]
    
    adcs_data = []
    models_data = []
    endpoints_data = []
    
    # Track unique ADCs, models, and endpoints
    unique_adcs = {}
    unique_models = {}
    
    for result in data:
        # Process ADC data
        if 'adc_name' in result and not isinstance(result.get('model'), dict) and not result.get('endpoint_name'):
            adc_name = result.get('adc_name', '')
            
            # Skip if we've already processed this ADC
            if adc_name in unique_adcs:
                continue
                
            # Consolidate citations into a single field with separator
            citations = result.get('citations', [])
            consolidated_citations = " || ".join(citations) if citations else ""
            
            adc_data = {
                'id': file_id,
                'adc_name': adc_name,
                'antibody_name': result.get('antibody_name', ''),
                'antibody_clonality': result.get('antibody_clonality', ''),
                'antibody_species': result.get('antibody_species', ''),
                'antibody_isotype': result.get('antibody_isotype', ''),
                'payload_name': result.get('payload_name', ''),
                'payload_target': result.get('payload_target', ''),
                'linker_name': result.get('linker_name', ''),
                'linker_type': result.get('linker_type', ''),
                'antigen_name': result.get('antigen_name', ''),
                'adc_citations': consolidated_citations
            }
            
            adcs_data.append(adc_data)
            unique_adcs[adc_name] = True
        
        # Process model data
        elif 'adc' in result and isinstance(result.get('model'), dict) and not result.get('endpoint_name'):
            adc = result.get('adc', {})
            adc_name = adc.get('adc_name', '')
            model = result.get('model', {})
            model_name = model.get('model_name', '')
            model_type = model.get('model_type', '')
            
            # Create a unique key for this model
            model_key = f"{adc_name}|{model_name}|{model_type}"
            
            # Skip if we've already processed this model
            if model_key in unique_models:
                continue
                
            # Consolidate citations into a single field with separator
            citations = model.get('citations', [])
            consolidated_citations = " || ".join(citations) if citations else ""
            
            model_data = {
                'id': file_id,
                'adc_name': adc_name,
                'model_name': model_name,
                # 'model_type': model_type,
                # 'experiment_type': model.get('experiment_type', ''),
                'cancer_type': model.get('cancer_type', ''),
                'cancer_subtype': model.get('cancer_subtype', ''),
                'model_citations': consolidated_citations
            }
            
            models_data.append(model_data)
            unique_models[model_key] = True
        
        # Process endpoint data
        elif 'adc_name' in result and 'model_name' in result and 'endpoint_name' in result:
            adc_name = result.get('adc_name', '')
            model_name = result.get('model_name', '')
            # model_type = result.get('model_type', '')
            endpoint_name = result.get('endpoint_name', '')
            endpoint_measurements = result.get('endpoint_measurements', [])
            
            # Process each measurement for this endpoint
            for measurement in endpoint_measurements:
                # Consolidate citations into a single field with separator
                citations = measurement.get('citations', [])
                consolidated_citations = " || ".join(citations) if citations else ""
                
                endpoint_data = {
                    'id': file_id,
                    'adc_name': adc_name,
                    'model_name': model_name,
                    'model_type': measurement.get('model_type', ''),
                    'experiment_type': measurement.get('experiment_type', ''),
                    'endpoint_name': endpoint_name,
                    'measured_value': measurement.get('measured_value', ''),
                    'measured_concentration': measurement.get('measured_concentration', ''),
                    'measured_timepoint': measurement.get('measured_timepoint', ''),
                    'measured_death_percentage': measurement.get('measured_death_percentage', ''),
                    'endpoint_citations': consolidated_citations
                }
                
                endpoints_data.append(endpoint_data)
    
    return adcs_data, models_data, endpoints_data

def process_directory(directory_path):
    """Process all JSON files in a directory and create Excel sheets."""
    all_adcs = []
    all_models = []
    all_endpoints = []
    
    for file in os.listdir(directory_path):
        if file.endswith('_results.json'):
            file_path = os.path.join(directory_path, file)
            adcs, models, endpoints = process_json_file(file_path)
            all_adcs.extend(adcs)
            all_models.extend(models)
            all_endpoints.extend(endpoints)
    
    # Convert to DataFrames
    adcs_df = pd.DataFrame(all_adcs)
    models_df = pd.DataFrame(all_models)
    endpoints_df = pd.DataFrame(all_endpoints)
    
    return adcs_df, models_df, endpoints_df

def create_excel(directory_path, output_file='adc_extraction_results.xlsx'):
    """Create Excel file with three sheets: ADCs, Models, and Endpoints."""
    adcs_df, models_df, endpoints_df = process_directory(directory_path)
    
    # Create Excel writer
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        adcs_df.to_excel(writer, sheet_name='ADCs', index=False)
        models_df.to_excel(writer, sheet_name='Models', index=False)
        endpoints_df.to_excel(writer, sheet_name='Endpoints', index=False)
    
    print(f"Excel file created: {output_file}")
    print(f"ADCs: {len(adcs_df)} entries")
    print(f"Models: {len(models_df)} entries")
    print(f"Endpoints: {len(endpoints_df)} entries")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Convert extraction pipeline results to Excel")
    parser.add_argument("--input-dir", type=str, required=True,
                       help="Directory containing extraction results JSON files")
    parser.add_argument("--output-file", type=str, default="adc_extraction_results.xlsx",
                       help="Output Excel file path")
    
    args = parser.parse_args()
    
    create_excel(args.input_dir, args.output_file)

