import argparse
import os
import pandas as pd
import asyncio
import sqlite3
import logging
import json
import traceback
from typing import List, Dict, Optional, Union, TypedDict, Annotated, Any, Tuple
from openai import AsyncAzureOpenAI
from pydantic_ai import Agent
from pydantic_ai.models.openai import OpenAIModel
from pydantic_ai.providers.openai import OpenAIProvider
from tqdm import tqdm
from dotenv import load_dotenv
from datetime import datetime
from pathlib import Path
from jinja2 import Template
from utils.extraction_pydantic_models import (
    AntibodyDrugConjugate, 
    ExperimentalModel, 
    Endpoint, 
    EndpointName,
    AntigenExpression,
    AntigenExpressionHScore,
    EC50_HalfMaximalEffectiveConcentration,
    TumorGrowthInhibition,
    Cmax_MaximumConcentration,
    HalfLifePeriod,
    Internalization,
    AntiTumorActivityDose,
    ToxicityFrequency,
    LethalDose,
    get_endpoint_model
)
from langgraph.graph import StateGraph, START, END
from langgraph.types import Send
from pydantic import BaseModel, Field
from dataclasses import dataclass, field
import asyncio
from langgraph.graph import StateGraph, START, END
from rate_limiter import OpenAIRateLimiter
import operator
from dotenv import load_dotenv
import logfire
from pydantic_ai.tools import RunContext

load_dotenv()
logfire.configure(scrubbing=False, token=os.getenv('LOGFIRE_WRITE_TOKEN'))
logfire.instrument_pydantic_ai()

logger = None

def setup_logging(logs_dir: str = "logs", logging_level: str = "INFO") -> logging.Logger:
    """Configure logging with both file and console handlers."""
    global logger
    
    # If logger is already initialized, return it
    if logger is not None:
        return logger
    
    # Create logs directory if it doesn't exist
    log_path = Path(logs_dir)
    log_path.mkdir(exist_ok=True)
    
    # Create a timestamped log file
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = log_path / f"extraction_pipeline_parallel_{timestamp}.log"
    
    # Set log level
    log_level = getattr(logging, logging_level.upper())
    
    # Create formatters
    console_formatter = logging.Formatter('%(levelname)s - %(message)s')
    file_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # Setup handlers
    console_handler = logging.StreamHandler()
    console_handler.setLevel(log_level)
    console_handler.setFormatter(console_formatter)
    
    # Use UTF-8 encoding for file handler
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setLevel(logging.DEBUG)  # Always log debug to file
    file_handler.setFormatter(file_formatter)
    
    # Configure logger
    logger = logging.getLogger()
    logger.setLevel(logging.DEBUG)
    logger.handlers = []  # Clear any existing handlers
    logger.addHandler(console_handler)
    logger.addHandler(file_handler)
    logger.propagate = False
    
    logger.info(f"Logging initialized. Log file: {log_file}")
    return logger

@dataclass(frozen=True)
class SharedContext:
    """Immutable core data for a sample that can be safely shared across parallel nodes.
    
    Attributes:
        raw_text (str): The raw text being processed
    """
    raw_text: str 
    
    def with_updates(self, **kwargs) -> "SharedContext":
        """Create a new instance with updated values."""
        return SharedContext(
            raw_text=kwargs.get('raw_text', self.raw_text)
        )

class MeasurementOpinionState(BaseModel):
    reasoning_for_opinion: str
    opinion: bool

client = AsyncAzureOpenAI(
    azure_endpoint=os.getenv("AZURE_OPENAI_ENDPOINT"),
    azure_deployment='gpt-4.1',
    api_key=os.getenv("AZURE_OPENAI_API_KEY"),
    api_version=os.getenv("AZURE_OPENAI_API_VERSION"),
)

# Initialize the OpenAI model with Azure provider
model = OpenAIModel(model_name='gpt-4.1', provider=OpenAIProvider(openai_client=client))
model_settings = {'temperature': 0.0, 'seed': 777}
# model_settings = {'reasoning_effort': 'high'}

# Initialize agents for each extraction step
adc_agent = Agent(
    model=model,
    system_prompt=open("prompts/adc_extraction_system_prompt.md").read(),
    result_type=List[AntibodyDrugConjugate],
    retries=3,
    result_retries=3,
    model_settings=model_settings,
    deps_type=SharedContext
)

model_agent = Agent(
    model=model,
    system_prompt=open("prompts/model_extraction_system_prompt.md").read(),
    # result_type=List[ExperimentalModel],
    retries=3,
    result_retries=3,
    model_settings=model_settings,
    deps_type=SharedContext
)

endpoint_agent = Agent(
    model=model,
    system_prompt=open("prompts/endpoint_extraction_system_prompt.md").read(),
    retries=20,
    result_retries=20,
    model_settings=model_settings,
    deps_type=SharedContext
)

check_endpoint_agent = Agent(
    model=model,
    system_prompt=open("prompts/endpoint_available_system_prompt.md").read(),
    result_type=EndpointName,
    retries=3,
    result_retries=3,
    model_settings=model_settings
)

measurement_opinion_agent = Agent(
    model=model,
    system_prompt=open("prompts/expert_opinion_system_prompt.md").read(),
    result_type=MeasurementOpinionState,
    retries=3,
    result_retries=3,
    model_settings=model_settings,
)

all_models_agent = Agent(
    model=model,    
    result_type=str,
    retries=3,
    system_prompt="You are an expert bioinformatician.",
    result_retries=3,
    model_settings=model_settings
)

# Initialize the rate limiter
rate_limiter = OpenAIRateLimiter(
    min_retry_delay=1.0,
    max_retry_delay=60.0,
    default_retry_delay=5.0,
    buffer_time=3.0,  # Add 1 second buffer to OpenAI's suggested wait time
    max_concurrent=1  # Adjust based on your OpenAI rate limits
)

# @rate_limiter.async_rate_limited
# async def run_all_models_agent(prompt):
#     return await all_models_agent.run(prompt)

@rate_limiter.async_rate_limited
async def run_adc_agent(prompt, deps: Optional[SharedContext] = None):
    return await adc_agent.run(prompt, deps=deps)

@rate_limiter.async_rate_limited
async def run_model_agent(prompt, result_type, deps: Optional[SharedContext] = None):
    return await model_agent.run(prompt, result_type=List[result_type], deps=deps)

@rate_limiter.async_rate_limited
async def run_check_endpoint_agent(prompt):
    return await check_endpoint_agent.run(prompt)

@rate_limiter.async_rate_limited
async def run_endpoint_agent_with_type(prompt, result_type, deps: Optional[SharedContext] = None):
    return await endpoint_agent.run(prompt, result_type=List[result_type], deps=deps)

@rate_limiter.async_rate_limited
async def run_measurement_opinion_agent(prompt):
    return await measurement_opinion_agent.run(prompt)

class ModelState(BaseModel):
    adc: AntibodyDrugConjugate
    model: ExperimentalModel

class AvailableEndpointState(BaseModel):
    adc: AntibodyDrugConjugate
    model: ExperimentalModel
    available_endpoint_name: str

class EndpointState(BaseModel):
    adc: AntibodyDrugConjugate
    model: ExperimentalModel
    endpoint_name: str
    endpoint_measurements: Union[
        List[AntigenExpression], 
        List[AntigenExpressionHScore], 
        List[EC50_HalfMaximalEffectiveConcentration], 
        List[TumorGrowthInhibition],
        List[Cmax_MaximumConcentration], 
        List[HalfLifePeriod], 
        List[Internalization], 
        List[AntiTumorActivityDose], 
        List[ToxicityFrequency], 
        List[LethalDose], 
        List[Endpoint]
    ]

class FinalEndpointState(BaseModel):
    adc_name: str
    model_name: str
    endpoint_name: str
    endpoint_measurements: Union[
        List[AntigenExpression], 
        List[AntigenExpressionHScore], 
        List[EC50_HalfMaximalEffectiveConcentration], 
        List[TumorGrowthInhibition],
        List[Cmax_MaximumConcentration], 
        List[HalfLifePeriod], 
        List[Internalization], 
        List[AntiTumorActivityDose], 
        List[ToxicityFrequency], 
        List[LethalDose], 
        List[Endpoint]
    ]

class FinalState(TypedDict):
    shared_context: SharedContext 
    adcs: List[AntibodyDrugConjugate]
    all_models: str
    models: Annotated[List[ModelState], operator.add]
    available_endpoints: Annotated[List[AvailableEndpointState], operator.add]
    endpoints: Annotated[List[EndpointState], operator.add]
    final_endpoints: Annotated[List[FinalEndpointState], operator.add]

@adc_agent.tool
def load_adc_names_into_memory(ctx: RunContext[SharedContext], reasoning_for_complete_names: str, adc_names: List[str]) -> str:
    """Load all identified ADC names into agent's memory"""
    logger.info(f"Loading {len(adc_names)} ADC names into memory")
    # This is a placeholder function; actual implementation may vary
    # Here we just log the names for demonstration purposes
    for name in adc_names:
        logger.debug(f"Loaded ADC name: {name}")
    
    return f"Loaded {len(adc_names)} ADC names into my memory."

@model_agent.tool
def load_model_names_into_memory(ctx: RunContext[SharedContext], adc_name: str, thought_process: str, model_names: List[str]) -> str:
    """Load all identified model names for a given ADC into agent's memory"""
    logger.info(f"Loading {len(model_names)} model names into memory for ADC: {adc_name}")
    # This is a placeholder function; actual implementation may vary
    # Here we just log the names for demonstration purposes
    for name in model_names:
        logger.debug(f"Loaded model name: {name}")

    return f"Loaded {len(model_names)} model names for ADC {adc_name} into my memory."

async def extract_adcs(state: FinalState) -> dict[str, List[AntibodyDrugConjugate]]:
    """Extract ADCs from the text"""
    logger.info("Extracting ADCs")
    user_prompt = Template(open("prompts/adc_extraction_user_prompt.md").read()).render(
        TEXT=state["shared_context"].raw_text
    )
    try:
        result = await run_adc_agent(user_prompt, deps=state["shared_context"])
        
        # Ensure adcs is always a list
        adcs = result.data if isinstance(result.data, list) else [result.data]
        logger.info(f"Extracted {len(adcs)} ADCs")
        
        return {"adcs": adcs}
        
    except Exception as e:
        logger.error(f"Error extracting ADCs: {str(e)}")
        # Return empty result on error
        return {"adcs": []}

async def extract_all_models_in_text(state: FinalState) -> dict[str, str]:
    """Extract all models mentioned in the text"""
    logger.info("Extracting all models from study as a free form text")
    user_prompt = Template(open("prompts/model_extraction_free_form_user_prompt.md").read()).render(
        TEXT=state["shared_context"].raw_text
    )
    
    try:
        all_models = await run_model_agent(user_prompt, result_type=str)

        logger.info(f"Extracted all models: {all_models.data}")
        return {"all_models": all_models.data}
        
    except Exception as e:
        logger.error(f"Error extracting all models: {str(e)}")
        return {"all_models": ""}


def continue_to_models(state: FinalState) -> List[Send]:
    return [
        Send(
            "extract_models",
            {
                "adc": adc,
                "raw_text": state["shared_context"],
                "all_models": state["all_models"]
            }
        )
        for adc in state["adcs"] if adc.adc_type == "Investigative"
    ]


async def extract_models(state: ModelState) -> dict[str, List[ModelState]]:
    """Extract models for a specific ADC"""
    adc = state["adc"]
    raw_text = state["raw_text"].raw_text
    all_models = state["all_models"]
    
    logger.info(f"Extracting models for ADC: {adc.adc_name}")
    
    models_data = []

    try:
        user_prompt = Template(open("prompts/model_extraction_user_prompt.md").read()).render(
            TEXT=raw_text,
            ALL_MODELS=all_models,
            ADC=adc.adc_name
        )

        result = await run_model_agent(user_prompt, result_type=ExperimentalModel ,deps=state["raw_text"])
        
        # Ensure models is always a list
        models = result.data if isinstance(result.data, list) else [result.data]
        logger.info(f"Extracted {len(models)} models for ADC: {adc.adc_name}")
        
        # Create a new ADCWithModels with the model information
        for model in models:
            models_data.append(ModelState(adc=adc, model=model))
        return {"models": models_data}
    
    except Exception as e:
        logger.error(f"Error extracting models for ADC {adc.adc_name}: {str(e)}")
        return {"models": models_data}

def continue_to_check_available_endpoint(state: FinalState) -> List[Send]:
    """Create sends for endpoint extraction"""
    sends = []
    for model_state in state["models"]:
        sends.append(
            Send(
                "check_available_endpoint",
                {
                    "adc": model_state.adc,
                    "model": model_state.model,
                    "raw_text": state["shared_context"].raw_text
                }
            )
        )
    return sends
   
async def check_available_endpoint(state: AvailableEndpointState) -> dict[str, List[AvailableEndpointState]]:
    """Extract available endpoints for a specific ADC and model"""
    adc = state["adc"].adc_name
    antigen = state["adc"].antigen_name
    model = state["model"]
    raw_text = state["raw_text"]
    
    logger.info(f"Extracting available endpoints for ADC: {adc}, Model: {model.model_name}")
    user_prompt = Template(open("prompts/endpoint_available_user_prompt.md").read()).render(
        TEXT=raw_text,
        ADC=adc,
        ANTIGEN=antigen,
        MODEL_NAME=model.model_name
    )
    list_of_available_endpoints = []
    try:
        result = await run_check_endpoint_agent(user_prompt)
        
        # Process the EndpointName object to extract only available endpoints
        endpoint_dict = result.data.model_dump()
        
        # Iterate through all fields in the EndpointName object
        for field_name, field_value in endpoint_dict.items():
                
            # Check if this endpoint is available
            if field_value is True:               
                # Add to available endpoints
                list_of_available_endpoints.append(
                    AvailableEndpointState(
                        adc=state['adc'],
                        model=model,
                        available_endpoint_name=field_name
                    )
                )
        
        logger.info(f"{len(list_of_available_endpoints)} endpoints are available for ADC: {adc}, Model: {model.model_name}")
        
        return {"available_endpoints": list_of_available_endpoints}
    except Exception as e:
        logger.error(f"Error extracting endpoint type for ADC {adc}, Model {model.model_name}: {str(e)}")
        logger.error(f"Exception details: {traceback.format_exc()}")
        return {"available_endpoints": list_of_available_endpoints}

def continue_to_endpoints(state: FinalState) -> List[Send]:

    sends = []
    for available_endpoint_state in state["available_endpoints"]:
        sends.append(
            Send(
                "extract_endpoints",
                {
                    "adc": available_endpoint_state.adc,
                    "model": available_endpoint_state.model,
                    "available_endpoint_name": available_endpoint_state.available_endpoint_name,
                    "raw_text": state["shared_context"]
                }
            )
        )
    return sends

async def extract_endpoints(state: EndpointState) -> dict[str, List[EndpointState]]:
    """Extract endpoint measurements for a specific ADC, model, and endpoint name"""
    adc = state["adc"].adc_name
    antigen = state["adc"].antigen_name
    model = state["model"].model_name
    available_endpoint_name = state["available_endpoint_name"]
    raw_text = state["raw_text"].raw_text
    
    endpoint_name = available_endpoint_name
    logger.info(f"Extracting endpoints for ADC: {adc}, Model: {model}, Endpoint: {endpoint_name}")
    
    user_prompt = Template(open("prompts/endpoint_extraction_user_prompt.md").read()).render(
        TEXT=raw_text,
        ADC=adc,
        ANTIGEN=antigen,
        MODEL=model,
        ENDPOINT=endpoint_name
    )
    
    try:
        # Get the appropriate result type based on the endpoint name
        result_type = get_endpoint_model(endpoint_name)
        
        # Run the agent with the dynamic result type
        result = await run_endpoint_agent_with_type(user_prompt, result_type, state["raw_text"])
        
        # Get endpoint measurements from result
        endpoint_measurements = result.data
        logger.info(f"Successfully extracted endpoint measurement for ADC: {adc}, Model: {model}, Endpoint: {endpoint_name}")
        
        return {"endpoints": [EndpointState(adc=state['adc'], model=state['model'], endpoint_name=endpoint_name, endpoint_measurements=endpoint_measurements)]}
    
    except Exception as e:
        logger.error(f"Error extracting endpoints for ADC {adc}, Model: {model}, Endpoint: {endpoint_name}: {str(e)}")
        logger.error(f"Exception details: {traceback.format_exc()}")
        return {"endpoints": []}
    
async def extract_final_endpoints(state: FinalState) -> dict[str, EndpointState]:
    """Discard unnecessary endpoint measurements based on expert opinion and save into final endpoints"""
    logger.info("Discarding unnecessary endpoint measurements")
    
    final_endpoints = []
    
    for endpoint_state in state["endpoints"]:
        adc_name = endpoint_state.adc.adc_name
        antigen_name = endpoint_state.adc.antigen_name
        model_name = endpoint_state.model.model_name
        endpoint_name = endpoint_state.endpoint_name

        final_endpoint = FinalEndpointState(
            adc_name=adc_name,  
            model_name=model_name,
            endpoint_name=endpoint_name,
            endpoint_measurements=[]
        )

        for measurement in endpoint_state.endpoint_measurements:
            # Get expert opinion on this measurement
            user_prompt = Template(open("prompts/expert_opinion_user_prompt.md").read()).render(
                ADC=adc_name,
                ANTIGEN=antigen_name,
                MODEL_NAME=model_name,
                ENDPOINT=endpoint_name,
                ENDPOINT_MEASUREMENT=measurement.model_dump_json()
            )
            
            try:
                result = await run_measurement_opinion_agent(user_prompt)
                
                if result.data.opinion:
                    # If opinion is positive, keep the measurement
                    final_endpoint.endpoint_measurements.append(measurement)
                    logger.info(f"Measurement for ADC {adc_name}, Model {model_name}, Endpoint {endpoint_name} is valid and will be saved.")
                else:
                    logger.warning(f"Measurement for ADC {adc_name}, Model {model_name}, Endpoint {endpoint_name} is NOT valid and will NOT be saved.")
                    
            except Exception as e:
                logger.error(f"Error providing expert opinion on measurement: {str(e)}")
                logger.debug(f"Exception details: {traceback.format_exc()}")

        if final_endpoint.endpoint_measurements:
            final_endpoints.append(final_endpoint)
                
    return {"final_endpoints": final_endpoints}

def build_parallel_pipeline():
    """Build the parallel extraction pipeline"""
    logger.info("Building parallel extraction pipeline")
    # 1) Instantiate graph
    graph = StateGraph(FinalState)

    # 2) Register nodes
    graph.add_node("extract_adcs", extract_adcs)
    graph.add_node("extract_all_models_in_text", extract_all_models_in_text)
    graph.add_node("extract_models", extract_models)
    graph.add_node("check_available_endpoint", check_available_endpoint)
    graph.add_node("extract_endpoints", extract_endpoints)
    graph.add_node("extract_final_endpoints", extract_final_endpoints)

    # 3) Define edges
    graph.add_edge(START, "extract_adcs")
    graph.add_edge("extract_adcs", "extract_all_models_in_text")
    graph.add_conditional_edges("extract_all_models_in_text", continue_to_models, ["extract_models"])
    graph.add_conditional_edges("extract_models", continue_to_check_available_endpoint, ["check_available_endpoint"])
    graph.add_conditional_edges("check_available_endpoint", continue_to_endpoints, ["extract_endpoints"])
    graph.add_edge("extract_endpoints", "extract_final_endpoints")
    graph.add_edge("extract_endpoints", END)
    
    return graph.compile()

async def process_file_parallel(md_file):
    """Process a markdown file using the parallel extraction pipeline"""
    
    # Read the input file
    with open(md_file, 'r', encoding='utf-8') as f:
        text = f.read()
    
    # Initialize state
    initial_state: FinalState = {
        "shared_context": SharedContext(raw_text=text),
        "adcs": [],
        "models": [],
        "available_endpoints": [],
        "endpoints": [],
        "final_endpoints": []
    }
    
    # Build and run pipeline
    compiled_graph = build_parallel_pipeline()
    # Asynchronously invoke the graph
    final_state = await compiled_graph.ainvoke(initial_state)
        
    return final_state['adcs'], final_state['models'], final_state['final_endpoints']

def save_results(adcs, models, endpoints, input_file, output_dir):
    """Save extraction results to JSON file."""
    try:
        logger.info(f"Saving results to {output_dir}")
        
        # Create output directory if it doesn't exist
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # Create output filename based on input filename
        input_filename = Path(input_file).stem
        output_file = output_path / f"{input_filename}_results.json"
        
        output_adcs = [adc.model_dump() for adc in adcs]
        output_models = [model.model_dump() for model in models]
        output_endpoints = [endpoint.model_dump() for endpoint in endpoints]

        # Combine all results into a single list
        output_data = []
        # get_metrics("model", output_models)
        output_data.extend(output_adcs)
        output_data.extend(output_models)
        output_data.extend(output_endpoints)
        
        # Save to JSON file
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, indent=2, ensure_ascii=False)
            
        logger.info(f"Results saved successfully to {output_file}")
        
    except Exception as e:
        logger.error(f"Error saving results: {str(e)}")
        logger.debug(f"Error details: {traceback.format_exc()}")
        raise

async def main():
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Extract information from scientific papers using parallel pipeline")
    parser.add_argument("--input-file", type=str, default="output/w2119870604.md", help="Input markdown file to process")
    parser.add_argument("--output-dir", type=str, default="output", help="Directory for output files")
    parser.add_argument("--logs-dir", type=str, default="logs", help="Directory for log files")
    parser.add_argument("--logging-level", type=str, default="DEBUG", 
                       choices=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
                       help="Set logging level")
    args = parser.parse_args()
    
    # Setup logging (only once)
    global logger
    if logger is None:
        logger = setup_logging(logs_dir=args.logs_dir, logging_level=args.logging_level)
    
    logger.info("Starting parallel extraction pipeline")
    
    try:
        # Create output directory
        output_dir = Path(args.output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # Process file and save results
        logger.info(f"Processing file: {args.input_file}")
        adcs, models, endpoints = await process_file_parallel(args.input_file)
        
        # Save results to JSON
        save_results(adcs, models, endpoints, args.input_file, output_dir)
        
        logger.info("Extraction pipeline completed successfully")
    except Exception as e:
        logger.error(f"Fatal error in extraction pipeline: {str(e)}")
        logger.debug(f"Error details: {traceback.format_exc()}")
        raise

if __name__ == "__main__":
    # Initialize logger at module level (only once)
    if logger is None:
        logger = setup_logging()
    
    # Run the main function
    asyncio.run(main())