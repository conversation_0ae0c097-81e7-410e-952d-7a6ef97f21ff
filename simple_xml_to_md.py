#!/usr/bin/env python3
"""
Simple XML to Markdown Converter

A straightforward script to convert XML files to Markdown format.
"""

import xml.etree.ElementTree as ET
import os


def xml_to_markdown_simple(xml_file_path: str, output_file_path: str):
    """
    Convert XML file to Markdown with simple formatting.
    
    Args:
        xml_file_path: Path to the input XML file
        output_file_path: Path to the output Markdown file
    """
    
    def clean_text(text):
        """Clean and normalize text."""
        if not text:
            return ""
        return text.strip().replace('\n', ' ').replace('\r', '')
    
    def process_element(element, level=0, output_lines=None):
        """Recursively process XML elements."""
        if output_lines is None:
            output_lines = []
        
        # Create heading based on element tag
        if element.tag:
            heading_level = min(level + 1, 6)  # Max 6 heading levels in markdown
            heading = "#" * heading_level + " " + element.tag.replace('_', ' ').replace('-', ' ').title()
            output_lines.append(heading)
            output_lines.append("")
        
        # Add element attributes if any
        if element.attrib:
            output_lines.append("**Attributes:**")
            for key, value in element.attrib.items():
                output_lines.append(f"- **{key}**: {value}")
            output_lines.append("")
        
        # Add element text content
        if element.text and element.text.strip():
            text_content = clean_text(element.text)
            if text_content:
                output_lines.append(text_content)
                output_lines.append("")
        
        # Process child elements
        for child in element:
            process_element(child, level + 1, output_lines)
        
        # Add tail text if present
        if element.tail and element.tail.strip():
            tail_content = clean_text(element.tail)
            if tail_content:
                output_lines.append(tail_content)
                output_lines.append("")
        
        return output_lines
    
    try:
        # Parse XML file
        tree = ET.parse(xml_file_path)
        root = tree.getroot()
        
        # Start conversion
        markdown_lines = []
        
        # Add document title
        markdown_lines.append(f"# {root.tag.replace('_', ' ').replace('-', ' ').title()}")
        markdown_lines.append("")
        
        # Add root attributes if any
        if root.attrib:
            markdown_lines.append("## Document Information")
            markdown_lines.append("")
            for key, value in root.attrib.items():
                markdown_lines.append(f"- **{key}**: {value}")
            markdown_lines.append("")
        
        # Add root text content if any
        if root.text and root.text.strip():
            markdown_lines.append("## Content")
            markdown_lines.append("")
            markdown_lines.append(clean_text(root.text))
            markdown_lines.append("")
        
        # Process all child elements
        for child in root:
            process_element(child, 0, markdown_lines)
        
        # Write to output file
        with open(output_file_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(markdown_lines))
        
        print(f"✅ Successfully converted XML to Markdown!")
        print(f"📄 Input: {xml_file_path}")
        print(f"📄 Output: {output_file_path}")
        print(f"📊 Generated {len(markdown_lines)} lines")
        
        return True
        
    except ET.ParseError as e:
        print(f"❌ XML Parse Error: {e}")
        return False
    except FileNotFoundError:
        print(f"❌ File not found: {xml_file_path}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def main():
    """Main function."""
    # File paths
    input_file = r"C:\Users\<USER>\Downloads\review.xml"
    output_file = r"C:\Users\<USER>\Downloads\review_markdown.md"
    
    # Convert XML to Markdown
    success = xml_to_markdown_simple(input_file, output_file)
    
    if success:
        print("\n🎉 Conversion completed successfully!")
        
        # Show a preview of the output
        try:
            with open(output_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            print(f"\n📋 Preview (first 15 lines):")
            print("-" * 60)
            for i, line in enumerate(lines[:15], 1):
                print(f"{i:2d}: {line.rstrip()}")
            
            if len(lines) > 15:
                print(f"... and {len(lines) - 15} more lines")
                
        except Exception as e:
            print(f"Could not show preview: {e}")
    else:
        print("\n❌ Conversion failed!")


if __name__ == "__main__":
    main()
