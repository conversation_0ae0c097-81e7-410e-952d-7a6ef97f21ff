# Implementation Plan for ADC Structured Data Extraction Pipeline

**Overview:**  
This plan details a comprehensive pipeline to extract structured data from preclinical Antibody-Drug Conjugate (ADC) study publications. We leverage a LangGraph-based multi-agent framework for orchestration, enabling specialized LLM agents (using GPT-4o or a local LLaMA 3.1 8B model) to perform document analysis and data extraction. The end product is a fully automated pipeline that populates a relational database with structured entities (i.e. Publication, ADC, Antibody, Payload, Linker, Endpoint), ready for expert review and analysis.

## 1. Refined Architecture of the Extraction Pipeline

**Pipeline Stages:** We refine the overall system into a sequence of stages from literature retrieval to database population. Each stage corresponds to a specific function, some powered by LLMs and others by deterministic processes. The stages are: 

1. **Literature Search & Filtering:** Use the OpenAlex Search API to find publications with ADC-related keywords (e.g., "antibody-drug conjugate") [#TODO-Include the complete query]. Apply filters such as (openaccess flag and published date) to narrow to relevant papers.  
2. **Abstract Screening (Classification):** For each candidate, run an LLM-based classifier on the abstract to determine if the paper is a *preclinical ADC study*. This step prunes out irrelevant hits (e.g., reviews or clinical trials). The LLM (e.g., LLaMA 3.1 8B instruct) is prompted to output a binary decision or score indicating relevance. High-confidence negatives are dropped; positives proceed to full text.  
3. **Full-Text Retrieval:** For each shortlisted publication, retrieve the full text content from BIKG shared data or the PubMed/PMC BioC API. The BioC format (XML) from PubMed Central is parsed to extract clean text. We convert the article into a structured text format (e.g., plain text or Markdown with section headings) for downstream processing.  
4. **Information Extraction (IE):** An orchestrated LLM-driven process parses the full text to extract structured data:
   - Identify and extract **ADC Composition**: the antibody (and its target antigen), the payload (toxin/drug), and the linker used.
   - Identify **Endpoints** reported in the study, categorized as safety, efficacy, pharmacokinetics (PK), pharmacodynamics (PD), or biomarker results. Extract descriptions or quantitative values for each endpoint category.
   - Capture publication metadata as needed (title, authors, etc., though these may be available from OpenAlex already).
5. **Data Structuring & Validation:** The extracted data is structured into instances of the Pydantic models (Publication, ADC, Antibody, Payload, Linker, Endpoint). Validation rules (via Pydantic) ensure types and formats are correct. If the LLM output is incomplete or inconsistent with the schema, the system can flag or attempt a corrective parse.  
6. **Database Population:** Insert the validated data into a relational database. The schema mirrors the Pydantic models, preserving relations between entities. This stage is fully automated with no human intervention.  
7. **Output Review (Expert Validation):** Although not part of the automated run, the final step involves domain experts reviewing a sample of the database entries for accuracy. Their feedback will be used to refine the pipeline’s prompts and rules, but this occurs *after* data extraction (i.e., human-in-the-loop only at the validation phase, not during extraction).

## 2. Agentic Framework Design with LangGraph

We adopt **LangGraph**, a graph-based agent orchestration framework, to design a multi-agent system for this pipeline. LangGraph allows us to define the pipeline as a directed graph of nodes (agents) with edges controlling data flow. Each agent has a specialized role, and the graph manages their sequence and state. This design brings modularity, making the complex extraction workflow more tractable by dividing it into focused sub-tasks. Below we define the key agents and the overall flow:

**Agent Roles:**

- **Search Agent:** A non-LLM node responsible for querying OpenAlex. It takes a query (or list of ADC-related keywords) from the graph state and calls the OpenAlex API. It outputs a list of candidate publication identifiers (DOIs or OpenAlex IDs) with basic metadata.  
- **Filter Agent (Pre-screening):** An LLM-powered agent that examines each candidate’s title and abstract. Prompted with instructions to decide if the study is a *preclinical ADC experiment*, it outputs a classification (Yes/No or a score). This agent uses a small language model (e.g., a local instruction-tuned model) to avoid API calls. Its prompt might include a few examples of abstracts labeled “ADC study” vs “Not relevant” to improve accuracy. Candidates marked “Yes” (or above a confidence threshold) are passed on.  
- **Full-Text Fetch Agent:** A tool-oriented agent that retrieves the article text. It uses the publication ID to fetch content from the internal database or calls an API (like NCBI E-utilities for PubMed Central in BioC format). This agent then parses the returned XML/JSON using a library (e.g., `bioc` for BioC XML or `xml.etree.ElementTree` for XML) to extract the article’s sections (introduction, methods, results, etc.). The parsed text (or Markdown) is stored in the graph state for downstream agents.  
- **Extraction Orchestrator Agent:** A high-level controller agent (could be implemented as a LangGraph composite node) that initiates the detailed extraction. It may invoke multiple sub-agents or tools in sequence to gather all required pieces of information from the text:
  - *ADC Entity Extraction Agent:* An LLM agent that focuses on identifying the ADC and its components. It is prompted with the full text (or relevant portions like the methods/intro) and tasked with extracting the names/details of the antibody, target antigen, payload, and linker. It outputs a structued JSON in the form of pydantic model for these entities.
  - *Endpoint Extraction Agent:* An LLM agent that focuses on experimental outcomes. It processes sections like Results and Discussion, extracting findings categorized into **safety**, **efficacy**, **PK**, **PD**, **biomarker** endpoints. It outputs these as a structured list (each endpoint with a category, description, and any quantitative result).
  - *Iterative/Refinement Agents:* If needed, additional sub-agents can refine the extraction. For example, a **Verification Agent** (LLM) can take the extracted data and cross-check against the text to verify consistency, or fill any obvious gaps. Another could format the combined ADC + Endpoints data into the final Pydantic schema format (as JSON) ready for validation.
- **Database Agent:** A tool agent responsible for taking the validated structured output and inserting it into the relational database. It uses an ORM (or direct SQL) mapping aligned to the Pydantic models. This agent ensures that any new Antibody/Payload/Linker entries are created or linked appropriately and that Publication and Endpoint records are saved.

**Agent Flow (Graph Edges and State):**  
- The **graph state** carries the data (e.g., current paper’s info) between agents. For each publication candidate, the flow is: Search → Filter → Fetch → Extract → Validate → Store.
- Edges enforce that:
  - Output of **Search Agent** (list of candidates) fans out into parallel branches through **Filter Agent** (one branch per candidate or a loop over candidates).
  - **Filter Agent** feeds into **Fetch Agent** only if a candidate is classified as relevant. (Irrelevant branches terminate without further action.)
  - **Fetch Agent** output (full text content) goes into the **Extraction Orchestrator**. The content is placed in state (e.g., `state.paper_text`).
  - Inside Extraction, the **ADC Entity Extraction** and **Endpoint Extraction** may run sequentially or in parallel. A sequential approach should be used because **Endpoint Extraction** should have context of **ADC Entity Extraction**  and LLM API has rate limits. For example, first run the ADC entity agent, then supply its results (antibody/payload names) into the prompt of the Endpoint agent to help it focus (e.g., “Given the ADC composed of antibody X and payload Y, extract endpoints…”).
  - The outputs from the sub-agents are merged in state (e.g., `state.extracted_ADC` and `state.extracted_endpoints`).
  - A **Validation/Formatting node** reads those and attempts to instantiate the Pydantic models. It handles errors (e.g., missing required field) by either simple rules or by calling a **Verification LLM agent** to re-evaluate the text for the missing piece.
  - Finally, the **Database Agent** is triggered with the final structured record. After database insertion, the pipeline for that paper is complete and the graph can proceed to the next paper (if not parallelized).

**Why an Agentic Graph:** This design provides modularity and control. Each agent focuses on a narrow task, which aligns with best practices for multi-agent systems — *specialized prompts yield better results than a giant monolithic prompt*. Using LangGraph, we can maintain state across these steps (e.g., share the ADC name found by one agent with another agent extracting endpoints) and handle branching logic easily. The graph framework also aids in debugging and scaling:
- We can replace or improve one agent (e.g., swap in a more powerful LLM for extraction) without disrupting others.
- We can monitor intermediate outputs at each node for errors, enabling easier identification of where a failure occurs (for instance, if the ADC Extraction Agent returns an empty result, we know to adjust that step’s prompt or logic).
- Memory persistence in LangGraph’s design means agents can recall earlier information if needed. In our context, this could allow an agent to accumulate partial results or for a later agent to reuse the context of what was already extracted (though we mostly pass needed data explicitly to keep things transparent).

**Agent Implementation Notes:**  
- The LLM-based agents (Filter, Extraction, etc.) will use the local LLM (LLaMA 3.1 8B instruct) for on-premise execution. LangGraph can integrate custom LLM calls (via HuggingFace Transformers or GPT-4 API). We will wrap the model invocation in a node that takes a prompt and returns the model’s completion. The prompt templates for each agent are stored and version-controlled (to ensure consistent behavior and easy updates).  
- Tools like the OpenAlex API call and database insertion are implemented as function nodes (non-LLM). LangGraph supports such tool use, allowing the LLM agents to delegate certain actions to code.  
- Error handling is built into the graph flow. If an agent produces an unexpected output (e.g., the Endpoint agent fails to find any endpoint but the paper should have some), the graph can route to a fallback. For example, a fallback could be an alternate prompt or retry mechanism. If all attempts fail, the system can log the paper for later manual review rather than producing wrong data. This ensures **robustness**: the pipeline will not silently produce incorrect entries without flagging them.

## 3. LLM-Based Extraction & Parsing Strategy

Extracting structured information from full-text scientific papers is the crux of this pipeline. The strategy combines careful **text preprocessing**, tailored **prompt engineering** for the LLM, and systematic **parsing/validation** of outputs.

**3.1 Full-Text Preprocessing (XML/Markdown to Plain Text):**  
Publications retrieved in XML or HTML (e.g., via BioC or publisher content) are first converted to plain text or lightweight Markdown:
- We strip boilerplate sections not needed for extraction. References, author info, and acknowledgments are removed to avoid confusing the LLM.
- We preserve **section headings** (Introduction, Methods, Results, Discussion, etc.) either by Markdown headings or simple markers. This provides structural cues to the LLM about where certain information is likely found. For example, the *Methods* section often contains details of the ADC (if the authors describe how they constructed or obtained it) and the *Results* section contains outcome measures.
- If the full text is very long (some journals include extensive supplementary text), we set a limit on content length per LLM call. Using the section markers, we can truncate or prioritize sections. In preclinical studies, the critical info (ADC details and key results) usually appears in Methods and Results/Discussion, so those sections are prioritized. We can omit a lengthy Introduction or background if needed to stay within context window.
- The cleaned text is stored as a string. We need to ensure encoding issues or special characters (like Greek letters, chemical structures) are handled or approximated (for instance, "μg/mL" should be preserved, and Unicode symbols should be replaced with text if the model might not handle them well).

**3.2 Prompt Design for Information Extraction:**  
We craft prompts to guide the LLM to output the required structured data. Two main prompt patterns will be used, corresponding to the two sub-tasks (ADC components and Endpoints):

- **ADC Components Extraction Prompt:** This prompt explicitly asks the LLM to identify the ADC and its parts. For example: 

  *System message:* "You are a biomedical information extraction agent."  
  *User prompt:* "Extract the details of the Antibody-Drug Conjugate (ADC) studied in the text below. Provide: 
   1) Antibody name and the target it binds, 
   2) Payload (toxin/drug) name, 
   3) Linker used to connect the payload to the antibody. 
  If any of these are not mentioned, state 'Not specified'. Provide the output in JSON with keys `antibody`, `target`, `payload`, `linker`. 
  Text: ```[FULL TEXT or relevant segment]```"

  Note that this example is a placeholder for direction of thought but not the finalized prompt.

  We might include a few-shot example (as system or user message prior to the text) showing a made-up paper excerpt and the expected JSON output using predefined pydantic models. This helps the model understand the format and level of detail. The model is instructed to only pull information from the text and not hallucinate values not present.

- **Endpoints Extraction Prompt:** This prompt asks for results grouped by category. For example:

  *System message:* "You are a biomedical results extraction assistant."  
  *User prompt:* "From the following text, extract the key experimental outcomes observed, categorized into `safety`, `efficacy`, `PK` (pharmacokinetics), `PD` (pharmacodynamics), and `biomarker` endpoints. For each category, list the relevant endpoint measures or findings. If a category was not evaluated, state 'None'. Provide the output in JSON with a list of endpoints, each having `category`, `description`, and if available a `value` and `unit`. 
  Text: ```[FULL TEXT of Results/Discussion]```"

  Note that this example is a placeholder for direction of thought but not the finalized prompt.

  Again, few-shot examples can be included (e.g., an example results paragraph and an example JSON listing an efficacy endpoint like tumor growth inhibition percentage, a safety endpoint like observed toxicity, etc.). This guides the LLM on how detailed to be and how to structure the data.

- **Combined Prompt (Optional):** Depending on context window size and model capability, we might combine the above into one prompt to get a single JSON with the full structure (ADC parts + endpoints under a publication object). For instance, instruct the model to output a JSON with top-level keys `publication` (metadata), `adc` (with sub-fields antibody, payload, linker…), and `endpoints` (list of categorized endpoint objects). However, combining increases complexity, so during development we can compare a one-shot approach to a two-step approach. A two-step approach (first ADC info, then endpoints) might be easier to manage and parse.

**3.3 Chunking instead of passing long texts:**  
If the full text exceeds the LLM’s context length (which can happen especially if using an 8B model with limited context):
- We split the text into logical chunks. For ADC components, we give preference to **Methods** and any part of **Results** or **Supplementary** where the ADC might be described (often there’s a section describing the drug or a table). We can programmatically search the text for known keywords like “antibody”, “drug”, “linker”, or specific payload names (e.g., "MMAE", "DM1") to find the segment where the ADC is described, and only feed that segment to the ADC Extraction prompt.
- For endpoints, we focus on **Results** and **Discussion** sections. If those are too long, we can break them by paragraphs or subheadings and process sequentially. For instance, if a paper has separate result subsections (e.g., *Efficacy in xenograft model*, *Toxicity in mice*), we could extract each separately. Alternatively, we feed as much as fits in one prompt and ask the model to list all categories; if some categories appear later in the text beyond the cutoff, a second pass will catch them.

**3.4 Parsing and Validation of LLM Output:**  
Since the LLM is asked to output JSON, we will likely get a JSON-like text. We will post-process this output carefully:
- Use a JSON parser in Python (`json.loads`) to load the LLM’s output. Because LLMs sometimes produce slightly invalid JSON (missing quotes, trailing commas), we might integrate a library like `pydantic` or `json5` or a custom function to correct common errors. Alternatively, use a structured prompting approach or **tool-assisted output formatting** (there are libraries like Guardrails or LangChain output parsers) to constrain the output. For example, *Guardrails AI* could validate the JSON against a schema and instruct the LLM to correct format if it fails ([Transforming Biomedical Knowledge Extraction with LLMs](https://www.linkedin.com/pulse/transforming-biomedical-knowledge-extraction-llms-heramb-patil-mzfkf#:~:text=LLMs%20are%20great%20in%20extracting,clinical%20trials%20and%20research%20papers)).
- Once parsed into a Python dict, we create Pydantic model instances: e.g., `adc = ADC(**adc_dict)`, `endpoints = [Endpoint(**ep) for ep in endpoints_list]`, etc. Pydantic will enforce types and required fields. If a field is missing or type mismatches, we catch the `ValidationError`.
- **Error handling:** If validation fails or the JSON is incomplete:
  - The system can prompt the LLM again, possibly with a refined prompt: e.g., “Some information was missing or incorrectly formatted. Please ensure to include X, Y, Z.” This would be done by a Validation agent that feeds back into the Extraction agent. Because we aim for no human-in-loop, this feedback has to be automated and generic.
  - If the JSON had minor format issues, our parser can fix them without bothering the LLM (e.g., adding a missing quote).
  - If certain fields are genuinely not present in the text (which might be fine, e.g., maybe no biomarker endpoints in a given study), we ensure the output uses a placeholder or `None` for them as per our schema, which is acceptable.
- We map or normalize certain values during parsing. For instance, if the LLM outputs the antibody name and target as a single string, we might split it or store them separately. Or if the LLM gave a percentage as "50 percent", we might convert to `50` and unit `%`. These normalization rules should be built into Pydantic validators for convenience.

**3.5 LLM Selection and Tuning:**  
For the extraction step, model choice is crucial:
- **GPT-4o (via API):** Would likely produce the most accurate and coherent extraction due to its superior understanding.This would not be a fully local solution, but could be an option for higher accuracy. 
- **LLaMA 3.1 8B instruct (local):** This is the primary choice for a local, offline pipeline. We will test if this model is capable of following the prompts and extracting the details reliably. We may need to adjust prompts or use a slightly larger local model if 8B struggles with comprehension of complex text. We can also explore fine-tuning or few-shot exemplars to improve performance.
- **Temperature & Determinism:** We will run the LLMs with a low temperature (e.g., 0 or 0.5) to maximize determinism and adherence to instructions. This helps reproducibility – running the pipeline twice on the same input should yield the same output. It also reduces hallucinations, though not entirely. If the model tends to hallucinate data (a known issue where even a high-performing models can introduce false info), we rely on the text-anchored prompts and the validation step to mitigate this.

**3.6 Enhancing Extraction Robustness:**  
To ensure the LLM-based extraction is robust:
- We incorporate knowledge of common ADC terminology into the prompts or a supplemental context. For example, provide the model a list of known payloads or linkers as a hint (so it doesn’t confuse a chemical name for something else).
- Use *tool-assisted extraction* for certain easy pieces: e.g., we could run a regex or dictionary lookup for known payload names in the text as a sanity check alongside the LLM. If the LLM misses it but a regex finds "MMAE", we know the payload is likely MMAE. This could trigger a re-prompt or manual correction later. The agent framework allows blending such tool results with LLM output (e.g., the extraction agent could call a Python function to find known terms, and then feed that info into the LLM prompt like “(Note: The text contains the term 'MMAE')”).
- The agents will include logic to handle multiple ADCs in one paper. If a paper compares two ADCs, the prompt can be written to allow multiple entries (e.g., output a list of ADC objects). The database schema (discussed later) will support multiple ADC records per publication. We will test this scenario explicitly.
- The entire extraction process (from prompt to JSON parse) is logged for each paper. This means if an expert later finds an error in an extracted field, we can trace back exactly what text or prompt led to that error, and adjust the pipeline accordingly (improve the prompt or add a rule).

By combining careful prompt design with automated parsing and error-checking, the LLM can reliably transform full-text information into the structured schema needed.

## 4. Performance Evaluation and Validation Strategy

To ensure the pipeline works correctly and meets quality standards, we define both **automated evaluation metrics** and a plan for **expert validation**:

**4.1 Automated Performance Metrics:**

- **Extraction Accuracy (Field-wise):** Using a test set of known papers, we will compare the extracted entities to a ground truth. This ground truth can be obtained by manually annotating a sample of publications or using data from known databases (if, for example, some ADCs are well-known and documented). We will measure precision and recall for each entity type:
  - *Antibody name and target:* Does the pipeline find the correct antibody and target antigen? (Precision = percentage of extracted antibodies that are correct; Recall = percentage of actual antibodies mentioned that were extracted.)
  - *Payload and Linker:* Similarly measure if the correct payload/linker were identified.
  - *Endpoints:* This is trickier to evaluate quantitatively, since endpoints can be phrased various ways. We may simplify evaluation by checking if key outcomes mentioned in the abstract or conclusion are captured in the structured output. An expert-derived list of expected outcomes per test paper can serve as reference.
- **False Positives/Negatives:** We log cases where the pipeline produced data for a non-ADC paper (false positive classification) or missed a paper that was relevant (false negative). The abstract classification agent’s performance can be evaluated by having an expert or a rule-based check on a random set of excluded papers to see if any ADC studies were mistakenly filtered out. We aim for high recall at that stage to minimize false negatives.
- **Throughput and Speed:** We will measure how many papers per minute or hour the pipeline can process on the local machine. This helps identify bottlenecks (likely the LLM calls). If using the 8B model locally, we note the inference time per document. If too slow, consider optimizations like model quantization or using a smaller model for certain steps. The pipeline should be reasonably efficient to handle a large set of papers (e.g., processing dozens of papers in a batch overnight).
- **Stability and Reproducibility:** We will run the pipeline multiple times on the same input set to ensure consistent output (especially important if any non-deterministic behavior remains). Any variations would indicate a need to further reduce randomness or fix an unstable parsing step.

**4.2 Quality Assurance with Domain Expert Review:**

- After the automated extraction is run on a larger set of publications, a domain expert (or a team) will manually review a subset of the structured outputs. They will check for correctness and completeness:
  - Are all important findings from the paper reflected in the structured data?
  - Are there any hallucinated entries (e.g., an endpoint that the paper did not actually report)?
  - Are units and magnitudes correctly captured (e.g., if an efficacy endpoint says “Tumor growth inhibited by 80%”, is that correctly in the data)?
- We will provide the expert with an interface or report that makes review easy, for example: each structured record alongside the paper title and maybe key sentences from the text that support each extracted field. This traceability helps the expert verify each piece. (We can generate supporting evidence by having the LLM highlight the source sentence for each data point during extraction.)
- The expert feedback will be collected systematically. If they find errors, we categorize them:
  - *Extraction Miss:* Something was in the text but the pipeline missed it. We then analyze why (e.g., prompt didn’t ask for it, or model failed, or text was oddly formatted).
  - *Extraction Incorrect:* The pipeline extracted something, but it’s wrong. Possibly a hallucination or a mismatch (e.g., wrong payload name). We determine if the error came from the model or a parsing issue.
  - *Schema Issue:* The information was extracted correctly but didn’t fit well into our schema. This might prompt a schema or model update (e.g., maybe an endpoint doesn’t neatly fall into one category).
- Based on these categories, we refine the system:
  - Update prompts or add few-shot examples to fix systematic extraction misses (for example, if the model often misses PD endpoints, provide an example to it of what a PD endpoint looks like).
  - Introduce new rules/agents if needed. For instance, if the expert notes hallucinations of data not in text, we might add a verification agent that double-checks each extracted item against the text (essentially asking the model, “find where the text supports this value”).
  - Expand the schema or adjust the Pydantic models if the domain requires it (e.g., maybe we need a field for “dose” if it turns out to be important for every efficacy endpoint).
- We will iteratively test any changes on the evaluation set until the automated metrics show improvement and the expert reviewers are satisfied with a majority of records.

**4.3 Continuous Evaluation and Monitoring:**

- After deployment, if new batches of papers are processed, we will maintain a log of pipeline outputs and periodically sample for quality. This ensures **robustness** over time and catches drift (if the literature style changes or if new ADC concepts arise that the model wasn’t trained on).
- We will incorporate unit tests for components where possible. For example, a test could feed a dummy paper text with known values through the extraction prompt and assert that the JSON contains those values. Another test can simulate a failure (like missing section) to see if the pipeline handles it without crashing.
- Because LLMs can be updated or changed, we will **pin the versions** of models and libraries used. If we ever upgrade (say to LLaMA 3.1 -> 3.2 or a different model), we run the evaluation suite again to ensure performance hasn’t regressed.

## 5. Database Schema Design (with Pydantic Model Alignment)

The structured data will be stored in a relational database. We design the schema to mirror the Pydantic models for **Publication**, **ADC**, **Antibody**, **Payload**, **Linker**, and **Endpoint**. This ensures an easy translation from model instances to database rows. We will likely use an SQL database (e.g., PostgreSQL or SQLite during development) with an ORM like SQLAlchemy or SQLModel for integration.

**5.1 Entities and Tables:**

- **Publication Table:** Stores metadata about the publication.
  - *Fields:* `publication_id` (PK, auto or could use DOI as primary key), `title`, `journal`, `year`, `doi`, `pubmed_id`, `abstract`, etc. It may also have flags like `is_preclinical_ADC` (True/False from the classification step, mostly True since we only store relevant ones).
  - Each Publication can link to one or multiple ADC entries (one study might evaluate multiple ADCs).
- **Antibody Table:** Stores details of antibodies.
  - *Fields:* `antibody_id` (PK), `name` (e.g., "Trastuzumab"), `target` (e.g., "HER2 receptor"), `isotype` (if mentioned, e.g., IgG1), `species` or source (if mentioned).
  - Antibodies are often reused across ADCs (e.g., the same antibody might be used for different payloads in different studies). So this can be a reference table. If the pipeline encounters an antibody name already in the table (exact match), it can reuse that ID. Otherwise, insert a new record.
- **Payload Table:** Stores payload (toxic drug) info.
  - *Fields:* `payload_id` (PK), `name` (e.g., "MMAE"), `class` (e.g., "Auristatin derivative"), maybe `mechanism` or `target` if available (many payloads are microtubule inhibitors, etc. – can store if present).
  - Like Antibodies, this can be reused. There are limited known payload types in ADCs, so many papers will mention one of a known set (DM1, DM4, MMAE, PBD, etc.).
- **Linker Table:** Stores linker info.
  - *Fields:* `linker_id` (PK), `name` (e.g., "vcMMAE linker" or a chemical name), `type` (e.g., cleavable dipeptide linker, non-cleavable, etc. if mentioned).
  - Also reusable if the exact same linker appears in multiple ADCs.
- **ADC Table:** Stores the ADC entity, linking Antibody, Payload, and Linker.
  - *Fields:* `adc_id` (PK), `name` (if the ADC has a specific name or code in the paper), `antibody_id` (FK to Antibody), `payload_id` (FK), `linker_id` (FK), possibly `target` (could be redundant if antibody has target, but could store for convenience).
  - Additionally, could have fields for *attributes of the ADC in this study*, such as `drug_to_antibody_ratio` (DAR) if reported, or `molecular_weight`, etc. We will align this with what the Pydantic ADC model contains. For example, if the Pydantic model has a field for DAR, we include it.
  - The ADC table is central – it ties to Publication via a junction or directly if one publication-one ADC. However, since one publication *can* study multiple ADCs, we should model a many-to-many: a junction table **Publication_ADC** with `publication_id` and `adc_id` might be needed. Alternatively, if it’s simpler and we assume usually one main ADC per paper, we could put a `publication_id` in ADC table (making it many-to-one where a pub can have many ADCs). The more normalized approach is junction, but we can decide based on typical cases.
- **Endpoint Table:** Stores study endpoints/results.
  - *Fields:* `endpoint_id` (PK), `publication_id` (FK to Publication), `adc_id` (FK to ADC, nullable if an endpoint is a general result not tied to a specific ADC – but most results will be per ADC treatment group), `category` (enum or text: one of "safety", "efficacy", "PK", "PD", "biomarker"), `description` (text of the result), `value` (numeric value if parsed), `unit` (e.g., "%" or "mg/kg", if a value is given), `time_point` (if relevant, e.g., day 30 tumor size), `notes` (any additional info).
  - We expect multiple endpoint records per publication (each important finding becomes one entry). For example, a paper might have two efficacy endpoints (tumor growth inhibition percentage and survival time), one safety endpoint (maximum tolerated dose), and one PK endpoint (half-life of the ADC in plasma). Each becomes one row with category and details.
  - The Endpoint table references Publication because context of which paper, and references ADC to know which ADC the result pertains to. If a study has only one ADC, the link is trivial; if multiple, we need to associate endpoints with the correct ADC (this will rely on how the extraction agent formats the data — likely grouping endpoints by ADC if multiple ADCs are present).
- **(Optional) Biomarker/Target Table:** If biomarker endpoints are complex (e.g., measuring a specific biomarker level), we might normalize those too. However, initially we can treat biomarker as just another endpoint category and store details in the description (for example: category = biomarker, description = "Phospho-ERK levels reduced by 50% in tumor cells").

**5.2 Relationships and Constraints:**

- **One-to-Many vs Many-to-Many:** An Antibody can be in many ADCs, a Payload in many ADCs, a Linker in many ADCs. ADC to Publication is many-to-many (one paper can have several ADCs; one ADC could possibly appear in multiple papers – e.g., if a known ADC is studied by different groups). The schema will reflect this:
  - Antibody, Payload, Linker have one-to-many relationships to ADC (via foreign keys in ADC).
  - ADC to Publication is many-to-many. Implementation: a linking table Publication_ADC(pub_id, adc_id) as mentioned, or an array of ADCs in a publication object if using a document DB, but since relational, we use linking.
  - Endpoint to Publication is many-to-one (each endpoint from a single study). Endpoint to ADC is many-to-one (each endpoint pertains to one ADC or null if general).
- **Primary Keys:** Use synthetic integer primary keys for internal linking (especially if using SQLite or Postgres). However, for Publication, the DOI or PubMed ID can be a natural key – we will enforce unique constraint on DOI to avoid duplicates. The pipeline should check before insertion if a publication already exists (to not double-insert on reruns).
- **Schema Alignment with Pydantic:** The Pydantic models presumably have attributes that match the above fields. We ensure naming consistency. For instance, if the Pydantic `Publication` model has `doi: str`, `title: str`, etc., our DB columns mirror those. If Pydantic `ADC` model nests `Antibody`, we translate that to the foreign key relationship (with possibly an ORM relationship to easily fetch the Antibody object).
- We might use an ORM like SQLModel (which is built on Pydantic and SQLAlchemy) to define these models only once and get both Pydantic and DB schema from it. This avoids duplication. Each class (Publication, ADC, etc.) can inherit from SQLModel which gives it a Pydantic nature and also mappable to a table.

**5.3 Example Schema Layout:** (simplified for illustration)

```sql
-- Publication table
CREATE TABLE publication (
  id SERIAL PRIMARY KEY,
  doi TEXT UNIQUE,
  title TEXT,
  journal TEXT,
  year INT,
  abstract TEXT,
  ... -- other metadata
);

-- Antibody table
CREATE TABLE antibody (
  id SERIAL PRIMARY KEY,
  name TEXT,
  target TEXT,
  isotype TEXT,
  UNIQUE(name, target) -- optional to avoid duplicates if same antibody+target entered twice
);

-- Payload table
CREATE TABLE payload (
  id SERIAL PRIMARY KEY,
  name TEXT,
  class TEXT,
  mechanism TEXT
);

-- Linker table
CREATE TABLE linker (
  id SERIAL PRIMARY KEY,
  name TEXT,
  type TEXT
);

-- ADC table
CREATE TABLE adc (
  id SERIAL PRIMARY KEY,
  name TEXT,
  antibody_id INT REFERENCES antibody(id),
  payload_id INT REFERENCES payload(id),
  linker_id INT REFERENCES linker(id),
  dar REAL,
  UNIQUE(antibody_id, payload_id, linker_id) -- optionally avoid exact duplicate combo
);

-- Publication_ADC linking table (if needed)
CREATE TABLE publication_adc (
  publication_id INT REFERENCES publication(id),
  adc_id INT REFERENCES adc(id),
  PRIMARY KEY(publication_id, adc_id)
);

-- Endpoint table
CREATE TABLE endpoint (
  id SERIAL PRIMARY KEY,
  publication_id INT REFERENCES publication(id),
  adc_id INT REFERENCES adc(id),
  category TEXT CHECK (category IN ('safety','efficacy','pk','pd','biomarker')),
  description TEXT,
  value REAL,
  unit TEXT,
  time_point TEXT,
  notes TEXT
);
```

This schema covers the core entities. It can be further normalized or denormalized depending on query needs, but it aligns well with the object model we aim to fill. For example, after extraction, we may create an `ADC` object with sub-objects for Antibody, Payload, Linker. We would save Antibody, Payload, Linker first (get their IDs), then save ADC with those IDs, then link the ADC to Publication, and insert Endpoint rows.

**5.4 Database Integration in Pipeline:**

- The Database Agent will use SQLAlchemy/SQLModel to add records. It will handle checking for existing entries. For instance, before adding a new antibody, it can query if an antibody with the same name (and maybe same target) exists and reuse it to avoid duplicates. The same for payload and linker.
- Transactions: Each publication’s data insertion can be done in a transaction such that if something fails halfway (e.g., a constraint violation), we roll back and log the error. This prevents partial data.
- After insertion, we can commit and optionally return the IDs or objects. The graph state could capture the inserted object IDs if needed for further use (not really needed after this, since it’s end-of-line for that data).
- The database can then be queried to generate analysis or reports across all extracted data. For example, one could query how many unique antibodies have been used in ADCs across these publications, or average efficacy outcomes for a certain payload, etc., which is the ultimate goal of structuring this data.

**5.5 Scalability and Maintenance:**

- If the database grows large (many papers, many endpoints), ensure indices on key fields (e.g., index on `publication.doi`, on `endpoint.category`, etc. for fast lookup).
- The schema is designed to accommodate future expansion. If new entity types or fields are needed (for example, if later we add a **Conjugation method** entity or a **Cell line used** as part of experiments), those can be added without breaking existing structure, using additional tables or columns.
- We will maintain migration scripts (if using Alembic for SQLAlchemy, for instance) so that the database can be reproduced or updated in a controlled manner, ensuring **reproducibility** of the environment.

## 6. Tools, Best Practices, and Ensuring Robustness

Finally, we summarize the key tools and practices that will be used throughout the project to ensure the solution is reliable and reproducible:

- **Programming Stack:** 100% Python-based. Core libraries include:
  - `requests` or similar for API calls (OpenAlex, PubMed).
  - `langchain` and `langgraph` for building the LLM agent workflow. LangGraph provides the graph execution and state management.
  - Hugging Face Transformers (for loading the LLaMA 8B model locally). Possibly use `transformers` with `AutoModelForCausalLM` and `AutoTokenizer` to load the model in 8-bit mode to save memory. If GPT-4 via API is used for some agents, then the OpenAI API Python client would be needed.
  - `pydantic` for data models (ensuring the extracted data conforms to the expected structure).
  - `sqlalchemy` or `sqlmodel` for database interactions. A lightweight alternative could be `sqlite3` module if using SQLite for early development.
  - `bioc` PyPI library or `lxml` for parsing BioC XML from PubMed.
  - (Optional) `guardrails` or `pydantic-ai` for enforcing output schema on the LLM if we choose to integrate those.
- **Prompt and Agent Versioning:** We will keep prompt templates in separate files or within configuration, with clear version control (e.g., using Git). This way, any change to a prompt or agent logic is tracked. If the output changes after a prompt tweak, we can trace why by looking at the diff. The same goes for LLM model version – if the model is updated or replaced, note the version. Reproducibility is ensured by being able to rerun an older version of the pipeline on the same input and get the original result.
- **No Human-in-the-Loop (during extraction):** The system is designed to run end-to-end automatically. To handle the absence of human intervention:
  - The agents must be able to make decisions or default choices. For example, if an agent is unsure about classification (edge-case abstract), it could either use a lower confidence threshold (include the paper just in case) or use a self-check prompt (“Why might this be an ADC paper or not?”) to make a final decision. But it cannot ask a person. Logging such borderline cases for later analysis is important.
  - For extraction uncertainties, the LLM should be instructed to not leave placeholders like "UNKNOWN" without prompting – instead it should attempt a best guess or say "Not specified" explicitly. This way the output is complete and doesn’t break the schema, and we know to interpret "Not specified" as either truly not in paper or model couldn’t find it.
- **Robustness to Errors:** We implement try/except and fallback strategies generously:
  - API calls: If OpenAlex fails or times out, retry a few times or backoff. If still fails for a query, log and continue with others.
  - LLM calls: If the local model crashes or runs out of memory, catch that. We might slice the input smaller and try again, or restart the model instance. In the worst case, skip the paper with an error log.
  - Parsing: If JSON parsing fails even after attempts, we log the raw output for debugging. The pipeline might skip storing that paper’s data to avoid corrupt entries. Those can be manually examined later.
  - Each agent’s operation is logged (possibly in a structured way, e.g., a JSON log with agent name, input, output summary, time taken).
- **Testing and Validation Environment:** We will test the pipeline on a smaller set of documents before full-scale run. Use unit tests for individual functions (e.g., XML parsing function test on a sample XML). Use integration tests for a full pipeline run on 1-2 known papers.
- **Reproducibility:** Aside from version control:
  - Document the environment (Python version, library versions). Possibly provide a `requirements.txt` or `environment.yml` to recreate it.
  - If using a local LLM, document how it was obtained or fine-tuned. If it’s a custom model checkpoint, we need to preserve that exact file. We might use a Docker container or a specific compute environment configuration so that the same model and code run in the future yields the same results.
  - Randomness: as noted, set seeds where applicable. Many transformer model calls allow a random seed. Setting it means the generated text is reproducible across runs (given same model and prompt). We'll do this for the classification and extraction LLM calls if possible. (For example, using `model.generate(..., seed=42)` or setting `torch.manual_seed`).
- **Privacy and Compliance:** Ensure that all data stays local after retrieval. If we consider using GPT-4 for some part, be mindful that the text of papers (though public) is being sent to an external API – if that’s a concern, stick to local models. For any internal or proprietary data (the question seems to focus on published studies so likely all public), remain with local processing.
- **Scalability and Maintenance:** As the system grows (more papers, possibly new data fields), periodically refactor and optimize:
  - If the LLM becomes a bottleneck, consider upgrading hardware (GPU for faster inference) or model (maybe a quantized 13B model if 8B is not sufficiently accurate).
  - Monitor database growth. If tens of thousands of entries, consider moving from SQLite to PostgreSQL for reliability.
  - Keep the code modular (the LangGraph design inherently helps). This means future developers (or the current team after months) can understand each part. We will write clear docstrings and perhaps a brief documentation of the whole pipeline for internal use.

**6.1 Recommended Tools Summary:**  
- *LangGraph (LangChain)* – for building the agent workflow, giving us fine control over multi-agent coordination.  
- *Transformers (HuggingFace)* – to load and run LLaMA or other local models for classification/extraction.  
- *OpenAI API (optional)* – for using GPT-4 in classification or extraction if needed (with caution regarding data privacy).  
- *BioC API / PyBioC* – to retrieve and parse full text from PubMed Central in a structured way, which is more reliable than raw PDF parsing.  
- *Pydantic/SQLModel* – to define data schemas and validate outputs, and to simplify storing them in a database.  
- *Guardrails AI (optional)* – to enforce response format from the LLM, reducing manual parsing errors.  
- *Logging* – Python’s `logging` module or an external logging system to record the pipeline operations for debugging and audit.  
- *Testing* – PyTest for automated tests on components; could also incorporate small sample papers as test cases in a CI pipeline to ensure nothing breaks with updates.