[{"data": {"html_url": "/data/local-files/?d=adc_html/w2119870604.html"}, "predictions": [{"model_version": "v1", "result": [{"id": "entity1", "from_name": "label", "to_name": "text", "type": "taxonomy", "value": {"start": 0, "end": 8, "text": "<PERSON>", "taxonomy": [["Entity"], ["Entity", "Person"]]}}, {"id": "entity2", "from_name": "label", "to_name": "text", "type": "taxonomy", "value": {"start": 18, "end": 24, "text": "OpenAI", "taxonomy": [["Entity"], ["Entity", "Organization"]]}}, {"id": "entity3", "from_name": "label", "to_name": "text", "type": "taxonomy", "value": {"start": 28, "end": 41, "text": "San Francisco", "taxonomy": [["Entity"], ["Entity", "Location"]]}}]}]}]