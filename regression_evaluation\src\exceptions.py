"""
Custom exceptions for the ADC evaluation system.

This module provides a comprehensive exception hierarchy for better
error handling and debugging throughout the system.
"""

from typing import Optional, Dict, Any


class ADCEvaluationError(Exception):
    """Base exception for all ADC evaluation errors."""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(message)
        self.details = details or {}


class DataLoadingError(ADCEvaluationError):
    """Raised when there's an error loading data files."""
    pass


class DataValidationError(ADCEvaluationError):
    """Raised when data validation fails."""
    pass


class PaperNotFoundError(DataLoadingError):
    """Raised when a paper's data files cannot be found."""
    
    def __init__(self, paper_id: str, missing_files: Optional[list] = None):
        message = f"Paper '{paper_id}' not found"
        if missing_files:
            message += f". Missing files: {', '.join(missing_files)}"
        super().__init__(message, {"paper_id": paper_id, "missing_files": missing_files})


class LLMError(ADCEvaluationError):
    """Base exception for LLM-related errors."""
    pass


class LLMTimeoutError(LLMError):
    """Raised when LLM request times out."""
    pass


class LLMQuotaExceededError(LLMError):
    """Raised when LLM quota is exceeded."""
    pass


class LLMResponseError(LLMError):
    """Raised when LLM response is invalid or unparseable."""
    pass


class MatchingError(ADCEvaluationError):
    """Raised when matching logic fails."""
    pass


class EvaluationError(ADCEvaluationError):
    """Raised when evaluation calculation fails."""
    pass


class ConfigurationError(ADCEvaluationError):
    """Raised when configuration is invalid."""
    pass


class FileSystemError(ADCEvaluationError):
    """Raised when file system operations fail."""
    pass