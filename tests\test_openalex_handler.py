import pytest
import json
import os
import sqlite3
from datetime import datetime
from pathlib import Path
from src.openalex_handler import OpenAlexClient, load_query_from_file, load_emails

# Test data
TEST_QUERY = {
    "filter": {
        "publication_year": "2023",
        "type": "article"
    }
}

TEST_WORK = {
    "id": "W123456789",
    "title": "Test Article",
    "abstract_inverted_index": {"test": [0, 5]},
    "publication_date": "2023-01-01",
    "type": "article"
}

@pytest.fixture
def temp_db_path(tmp_path):
    """Create a temporary database path."""
    return str(tmp_path / "test_openalex.db")

@pytest.fixture
def temp_query_file(tmp_path):
    """Create a temporary query file."""
    query_path = tmp_path / "test_query.json"
    with open(query_path, 'w') as f:
        json.dump(TEST_QUERY, f)
    return str(query_path)

@pytest.fixture
def temp_emails_file(tmp_path):
    """Create a temporary emails file."""
    emails_path = tmp_path / "test_emails.txt"
    with open(emails_path, 'w') as f:
        f.write("<EMAIL>\<EMAIL>\n")
    return str(emails_path)

@pytest.fixture
def client(temp_db_path, temp_emails_file):
    """Create an OpenAlexClient instance for testing."""
    emails = load_emails(temp_emails_file)
    return OpenAlexClient(email_addresses=emails, db_path=temp_db_path)

def test_load_query_from_file(temp_query_file):
    """Test loading query from file."""
    query = load_query_from_file(temp_query_file)
    assert query == TEST_QUERY

def test_load_emails(temp_emails_file):
    """Test loading emails from file."""
    emails = load_emails(temp_emails_file)
    assert len(emails) == 2
    assert "<EMAIL>" in emails
    assert "<EMAIL>" in emails

def test_query_hash_generation(client):
    """Test that query hash generation is consistent."""
    hash1 = client._get_query_hash(TEST_QUERY)
    hash2 = client._get_query_hash(TEST_QUERY)
    assert hash1 == hash2

def test_checkpoint_save_and_load(client):
    """Test saving and loading checkpoints."""
    query_hash = client._get_query_hash(TEST_QUERY)
    
    # Save checkpoint
    client._save_checkpoint(
        query_hash=query_hash,
        next_cursor="test_cursor",
        total_collected=100,
        total_count=1000,
        query_params=TEST_QUERY,
        status="in_progress"
    )
    
    # Load checkpoint
    checkpoint = client.db.get_checkpoint(query_hash)
    assert checkpoint is not None
    assert checkpoint["next_cursor"] == "test_cursor"
    assert checkpoint["total_collected"] == 100
    assert checkpoint["total_count"] == 1000
    assert checkpoint["status"] == "in_progress"

def test_duplicate_work_prevention(client):
    """Test that duplicate works are not inserted."""
    # Insert work first time
    client.db.upsert_work(TEST_WORK)
    
    # Try to insert same work again
    client.db.upsert_work(TEST_WORK)
    
    # Verify only one record exists
    with client.db.conn:
        cursor = client.db.conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM dim_openalex_works WHERE id = ?", (TEST_WORK["id"],))
        count = cursor.fetchone()[0]
        assert count == 1

def test_work_upsert_updates_existing(client):
    """Test that upserting a work updates existing record."""
    # Insert initial work
    initial_work = TEST_WORK.copy()
    client.db.upsert_work(initial_work)
    
    # Update work with new title
    updated_work = TEST_WORK.copy()
    updated_work["title"] = "Updated Title"
    client.db.upsert_work(updated_work)
    
    # Verify update
    with client.db.conn:
        cursor = client.db.conn.cursor()
        cursor.execute("SELECT title FROM dim_openalex_works WHERE id = ?", (TEST_WORK["id"],))
        title = cursor.fetchone()[0]
        assert title == "Updated Title"

def test_query_completion_status(client):
    """Test query completion status tracking."""
    query_hash = client._get_query_hash(TEST_QUERY)
    
    # Initially should not be completed
    assert not client._is_query_completed(query_hash)
    
    # Mark as completed
    client._save_checkpoint(
        query_hash=query_hash,
        next_cursor=None,
        total_collected=1000,
        total_count=1000,
        query_params=TEST_QUERY,
        status="completed"
    )
    
    # Should now be completed
    assert client._is_query_completed(query_hash)

def test_checkpoint_resume(client):
    """Test resuming from checkpoint."""
    query_hash = client._get_query_hash(TEST_QUERY)
    
    # Save initial checkpoint
    client._save_checkpoint(
        query_hash=query_hash,
        next_cursor="resume_cursor",
        total_collected=500,
        total_count=1000,
        query_params=TEST_QUERY,
        status="in_progress"
    )
    
    # Load checkpoint
    next_cursor, total_collected = client._load_checkpoint(query_hash)
    assert next_cursor == "resume_cursor"
    assert total_collected == 500

def test_database_cleanup(client):
    """Test that database connection is properly closed."""
    client.db.close()
    assert client.db.conn is None or client.db.conn.closed

def test_completed_query_not_reexecuted(client, temp_query_file):
    """Test that a completed query is not re-executed."""
    # First execution
    query_params = load_query_from_file(temp_query_file)
    success = client.download_works(query_params)
    assert success
    
    # Mark query as completed
    query_hash = client._get_query_hash(query_params)
    client._save_checkpoint(
        query_hash=query_hash,
        next_cursor=None,
        total_collected=1000,
        total_count=1000,
        query_params=query_params,
        status="completed"
    )
    
    # Second execution should return immediately
    success = client.download_works(query_params)
    assert success
    
    # Verify that the query was marked as completed
    checkpoint = client.db.get_checkpoint(query_hash)
    assert checkpoint is not None
    assert checkpoint["status"] == "completed"
    assert checkpoint["total_collected"] == 1000
    assert checkpoint["total_count"] == 1000

if __name__ == "__main__":
    pytest.main([__file__]) 