#!/usr/bin/env python3
"""
CLI script for converting CSV files to JSON format for ADC evaluation.
"""

import argparse
import sys
from pathlib import Path
from src.transform.csv_converter import CSVConverter
from src.utils.logging_utils import get_logger

logger = get_logger(__name__)

def main():
    """Main CLI function."""
    parser = argparse.ArgumentParser(
        description="Convert ADC CSV files to JSON format for evaluation"
    )
    
    parser.add_argument(
        '--csv-file',
        type=str,
        required=True,
        help='Path to the input CSV file'
    )
    
    parser.add_argument(
        '--output-dir',
        type=str,
        default='data/results',
        help='Directory to save JSON files (default: data/results)'
    )
    
    parser.add_argument(
        '--paper-id',
        type=str,
        help='Convert specific paper ID only'
    )
    
    parser.add_argument(
        '--validate-only',
        action='store_true',
        help='Only validate conversion without saving files'
    )
    
    parser.add_argument(
        '--verbose',
        action='store_true',
        help='Enable verbose logging'
    )
    
    args = parser.parse_args()
    
    # Set up logging
    if args.verbose:
        logger.setLevel('DEBUG')
    
    try:
        # Initialize converter
        converter = CSVConverter(args.csv_file)
        
        # Load CSV files
        logger.info("Loading CSV files...")
        converter.load_csv_files()
        
        if args.paper_id:
            # Convert specific paper
            logger.info(f"Converting paper {args.paper_id}...")
            
            if args.validate_only:
                paper_data = converter.process_paper(args.paper_id)
                logger.info(f"Validation successful: {len(paper_data)} endpoints found")
                
                # Print sample data
                if paper_data:
                    logger.info("Sample record:")
                    import json
                    print(json.dumps(paper_data[0], indent=2))
            else:
                converter.save_paper_json(args.paper_id, args.output_dir)
                logger.info(f"Successfully converted paper {args.paper_id}")
        else:
            # Convert all papers
            logger.info("Converting all papers...")
            
            if args.validate_only:
                all_data = converter.convert_all_papers()
                logger.info(f"Validation successful: {len(all_data)} papers converted")
                
                # Print summary
                for paper_id, data in all_data.items():
                    logger.info(f"Paper {paper_id}: {len(data)} endpoints")
            else:
                converter.save_all_papers_json(args.output_dir)
                logger.info("Successfully converted all papers")
        
    except Exception as e:
        logger.error(f"Error during conversion: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()