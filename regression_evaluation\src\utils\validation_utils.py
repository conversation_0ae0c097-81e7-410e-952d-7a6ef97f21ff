"""
Validation utility functions for the ADC evaluation system.

This module provides centralized validation logic with comprehensive
error reporting and logging capabilities.
"""

import logging
from typing import Dict, List, Any, Optional, Set
from models import GroundTruthDocument, ExtractionRow, GTEndpoint, ExtractedEndpoint
from exceptions import DataValidationError
from .constants import Constants


logger = logging.getLogger(__name__)


class ValidationUtils:
    """Utility class for data validation operations."""
    
    @staticmethod
    def validate_ground_truth(gt_doc: GroundTruthDocument) -> Dict[str, Any]:
        """Validate ground truth document and return validation report."""
        errors = []
        warnings = []
        stats = {
            "total_endpoints": len(gt_doc.endpoints),
            "unique_adcs": set(),
            "unique_models": set(),
            "unique_endpoints": set()
        }
        
        # Validate paper_id
        if not gt_doc.paper_id or gt_doc.paper_id.strip() == "":
            errors.append("Missing or empty paper_id")
        
        # Validate endpoints
        if not gt_doc.endpoints:
            errors.append("No endpoints found in ground truth")
        
        for i, endpoint in enumerate(gt_doc.endpoints):
            ValidationUtils._validate_gt_endpoint(endpoint, i, errors, warnings, stats)
        
        # Convert sets to counts for stats
        stats["unique_adcs"] = len(stats["unique_adcs"])
        stats["unique_models"] = len(stats["unique_models"])
        stats["unique_endpoints"] = len(stats["unique_endpoints"])
        
        validation_report = {
            "is_valid": len(errors) == 0,
            "errors": errors,
            "warnings": warnings,
            "stats": stats
        }
        
        if errors:
            logger.error(f"Ground truth validation failed: {len(errors)} errors")
            for error in errors:
                logger.error(f"  - {error}")
        
        if warnings:
            logger.warning(f"Ground truth validation warnings: {len(warnings)} warnings")
            for warning in warnings:
                logger.warning(f"  - {warning}")
        
        return validation_report
    
    @staticmethod
    def _validate_gt_endpoint(
        endpoint: GTEndpoint, 
        index: int, 
        errors: List[str], 
        warnings: List[str], 
        stats: Dict[str, Any]
    ) -> None:
        """Validate a single ground truth endpoint."""
        prefix = f"Endpoint {index + 1}"
        
        # Check required fields
        if not endpoint.paper_id:
            errors.append(f"{prefix}: Missing paper_id")
        if not endpoint.adc_name:
            errors.append(f"{prefix}: Missing adc_name")
        if not endpoint.model_name:
            errors.append(f"{prefix}: Missing model_name")
        if not endpoint.endpoint_name:
            errors.append(f"{prefix}: Missing endpoint_name")
        
        # Basic value type check - no domain-specific assumptions
        if endpoint.endpoint_value is not None:
            if not isinstance(endpoint.endpoint_value, (int, float, str)):
                warnings.append(f"{prefix}: Unexpected endpoint value type: {type(endpoint.endpoint_value)}")
        
        # Collect stats
        if endpoint.adc_name:
            stats["unique_adcs"].add(endpoint.adc_name)
        if endpoint.model_name:
            stats["unique_models"].add(endpoint.model_name)
        if endpoint.endpoint_name:
            stats["unique_endpoints"].add(endpoint.endpoint_name)
    
    @staticmethod
    def validate_extraction_results(extraction_rows: List[ExtractionRow]) -> Dict[str, Any]:
        """Validate extraction results and return validation report."""
        errors = []
        warnings = []
        stats = {
            "total_rows": len(extraction_rows),
            "endpoint_rows": 0,
            "unique_adcs": set(),
            "unique_models": set(),
            "unique_endpoints": set()
        }
        
        if not extraction_rows:
            errors.append("No extraction rows found")
        
        for i, row in enumerate(extraction_rows):
            ValidationUtils._validate_extraction_row(row, i, errors, warnings, stats)
        
        # Convert sets to counts for stats
        stats["unique_adcs"] = len(stats["unique_adcs"])
        stats["unique_models"] = len(stats["unique_models"])
        stats["unique_endpoints"] = len(stats["unique_endpoints"])
        
        validation_report = {
            "is_valid": len(errors) == 0,
            "errors": errors,
            "warnings": warnings,
            "stats": stats
        }
        
        if errors:
            logger.error(f"Extraction results validation failed: {len(errors)} errors")
            for error in errors:
                logger.error(f"  - {error}")
        
        if warnings:
            logger.warning(f"Extraction results validation warnings: {len(warnings)} warnings")
            for warning in warnings:
                logger.warning(f"  - {warning}")
        
        return validation_report
    
    @staticmethod
    def _validate_extraction_row(
        row: ExtractionRow, 
        index: int, 
        errors: List[str], 
        warnings: List[str], 
        stats: Dict[str, Any]
    ) -> None:
        """Validate a single extraction row."""
        prefix = f"Row {index + 1}"
        
        # Check if it's an endpoint row (optimized architecture)
        if hasattr(row, 'type') and row.type == Constants.ROW_TYPE_ENDPOINT:
            stats["endpoint_rows"] += 1
            
            # Check required fields for endpoints
            if not row.paper_id:
                errors.append(f"{prefix}: Missing paper_id")
            if not row.adc_name:
                errors.append(f"{prefix}: Missing adc_name")
            if not row.model_name:
                errors.append(f"{prefix}: Missing model_name")
            if not row.endpoint_name:
                errors.append(f"{prefix}: Missing endpoint_name")
            
            # Check measurements
            if not row.endpoint_measurements:
                warnings.append(f"{prefix}: No endpoint measurements found")
            else:
                for j, measurement in enumerate(row.endpoint_measurements):
                    if not measurement.measurement_id:
                        warnings.append(f"{prefix}, Measurement {j + 1}: Missing measurement_id")
                    if not measurement.value:
                        warnings.append(f"{prefix}, Measurement {j + 1}: Missing value")
            
            # Collect stats
            if row.adc_name:
                stats["unique_adcs"].add(row.adc_name)
            if row.model_name:
                stats["unique_models"].add(row.model_name)
            if row.endpoint_name:
                stats["unique_endpoints"].add(row.endpoint_name)
        else:
            warnings.append(f"{prefix}: Non-endpoint row found (type: {getattr(row, 'type', 'unknown')})")
    
    @staticmethod
    def validate_paper_id_consistency(
        gt_doc: GroundTruthDocument, 
        extraction_rows: List[ExtractionRow], 
        expected_paper_id: str
    ) -> Dict[str, Any]:
        """Validate that paper IDs are consistent across all data."""
        errors = []
        warnings = []
        
        # Check ground truth paper ID
        if gt_doc.paper_id.lower() != expected_paper_id.lower():
            errors.append(f"Ground truth paper_id '{gt_doc.paper_id}' doesn't match expected '{expected_paper_id}'")
        
        # Check extraction rows paper IDs
        inconsistent_rows = []
        for i, row in enumerate(extraction_rows):
            if hasattr(row, 'paper_id') and row.paper_id.lower() != expected_paper_id.lower():
                inconsistent_rows.append(f"Row {i + 1}: {row.paper_id}")
        
        if inconsistent_rows:
            errors.append(f"Extraction rows with inconsistent paper_id: {', '.join(inconsistent_rows)}")
        
        return {
            "is_valid": len(errors) == 0,
            "errors": errors,
            "warnings": warnings
        }
    
    @staticmethod
    def validate_required_fields(data: Dict[str, Any], required_fields: List[str], context: str = "") -> List[str]:
        """Validate that all required fields are present and non-empty."""
        errors = []
        prefix = f"{context}: " if context else ""
        
        for field in required_fields:
            if field not in data:
                errors.append(f"{prefix}Missing required field '{field}'")
            elif data[field] is None or (isinstance(data[field], str) and data[field].strip() == ""):
                errors.append(f"{prefix}Empty required field '{field}'")
        
        return errors