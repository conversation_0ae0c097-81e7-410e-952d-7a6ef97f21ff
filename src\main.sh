#!/bin/bash

# Function to check if a command succeeded
check_status() {
    if [ $? -ne 0 ]; then
        log "ERROR: $1 failed. Exiting."
        exit 1
    fi
}

# Function to check if environment variable is set
check_env_var() {
    if [ -z "${!1}" ]; then
        log "ERROR: Environment variable $1 is not set. Please check your .env file."
        exit 1
    fi
}

# Load environment variables
if [ -f .env ]; then
    echo "Loading environment variables from .env file..."
    set -a
    source .env
    set +a
else
    echo "ERROR: .env file not found. Please create one based on .env.example"
    exit 1
fi

# print all required environment variables
echo "Loaded environment variables:"
echo "AZURE_OPENAI_ENDPOINT: $AZURE_OPENAI_ENDPOINT"
echo "AZURE_OPENAI_API_KEY: $AZURE_OPENAI_API_KEY"
echo "AZURE_OPENAI_API_VERSION: $AZURE_OPENAI_API_VERSION"
echo "AZURE_GPT_4O: $AZURE_GPT_4O"
echo "DATABASE_PATH: $DATABASE_PATH"
echo "DATA_PATH: $DATA_PATH"
echo "LOG_PATH: $LOG_PATH"

# Set up logging
LOG_DIR="${LOG_PATH}"
mkdir -p "$LOG_DIR"
LOG_FILE="$LOG_DIR/main_$(date +%Y%m%d_%H%M%S).log"

# Function to log messages
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# Create necessary directories
mkdir -p "${DATABASE_PATH}" "${DATA_PATH}/publication_files" config

# Step 1: Run OpenAlex Handler
log "Starting OpenAlex Handler..."
python -m src.openalex_handler download \
    --query-file config/openalex_query.json \
    --db-path "${DATABASE_PATH}/openalex_data.db" \
    --logs-dir "${LOG_PATH}" \
    --logging-level INFO
check_status "OpenAlex Handler"

# Step 2: Run Europe PMC Downloader and Publication Profiler in parallel
log "Starting Europe PMC Downloader and Publication Profiler in parallel..."

# Start Europe PMC Downloader in background
log "Launching Europe PMC Downloader..."
python -m src.europe_pmc_downloader \
    --pmcids_db "${DATABASE_PATH}/openalex_data.db" \
    --download-directory "${DATA_PATH}/publication_files" \
    --db-path "${DATABASE_PATH}/publication_files.db" \
    --logs-dir "${LOG_PATH}" \
    --logging-level INFO &
EUROPE_PMC_PID=$!

# Start Publication Profiler in background
log "Launching Publication Profiler..."
python -m src.publication_profiler \
    --openalex_db_path "${DATABASE_PATH}/openalex_data.db" \
    --db_path "${DATABASE_PATH}/publication_profiles.db" \
    --batch_size 50 \
    --logs-dir "${LOG_PATH}" \
    --logging-level INFO &
PUBLICATION_PROFILER_PID=$!

# Wait for both processes to complete
log "Waiting for parallel processes to complete..."
wait $EUROPE_PMC_PID
EUROPE_PMC_STATUS=$?
wait $PUBLICATION_PROFILER_PID
PUBLICATION_PROFILER_STATUS=$?

# Check the status of both processes
if [ $EUROPE_PMC_STATUS -ne 0 ]; then
    log "ERROR: Europe PMC Downloader failed with status $EUROPE_PMC_STATUS"
    exit 1
fi

if [ $PUBLICATION_PROFILER_STATUS -ne 0 ]; then
    log "ERROR: Publication Profiler failed with status $PUBLICATION_PROFILER_STATUS"
    exit 1
fi

log "All parallel processes completed successfully!"
log "All scripts completed successfully!" 