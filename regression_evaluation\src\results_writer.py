"""
Results writer implementations for the ADC evaluation system.

This module provides implementations of the ResultsWriter interface for
saving evaluation results in various formats.

TEST FILES: tests/test_main.py, tests/test_refactored_code.py, tests/test_gt_removal.py, tests/test_integration_real_api.py
IMPORTANT: When modifying this file, ensure corresponding test cases in the test files
are updated or added to maintain test coverage and validate the changes.
"""

from typing import Optional, Dict, Any
from pathlib import Path
import sys
from interfaces import ResultsWriter
from models import EvaluationResult
from exceptions import FileSystemError
from utils.file_utils import FileUtils
from utils.logging_utils import LoggerMixin, log_async_function_call
from utils.constants import Constants


class JSONResultsWriter(ResultsWriter, LoggerMixin):
    """JSON-based results writer for evaluation results."""
    
    def __init__(self, data_dir: str = Constants.DEFAULT_DATA_DIR):
        """Initialize results writer with data directory."""
        self.data_dir = data_dir
        self.logger.info(f"Initialized JSON results writer with data directory: {data_dir}")
    
    def write_results(self, results: EvaluationResult, output_path: Optional[str] = None) -> str:
        """Write evaluation results to JSON file."""
        try:
            # Determine output path
            if output_path:
                final_path = Path(output_path)
            else:
                final_path = self._get_default_output_path(results.paper_id)
            
            self.logger.debug(f"Writing results to: {final_path}")
            
            results_dict = {
                "paper_id": results.paper_id,
                "TP": results.TP,
                "FP": results.FP,
                "FN": results.FN,
                "precision": results.precision,
                "recall": results.recall,
                "f1": results.f1,
                "llm_vs_human_corr_overall": results.llm_vs_human_corr_overall,
                "llm_vs_human_corr_by_endpoint": results.llm_vs_human_corr_by_endpoint,
                "detailed_results": [r.model_dump() for r in results.detailed_results],
                "unmatched_gt_items": results.unmatched_gt_items
            }
            
            # Add metadata
            results_dict["_metadata"] = {
                "format_version": "1.0",
                "writer_type": "JSONResultsWriter",
                "total_reasoning_tokens": sum(
                    detail.llm_response.reasoning_tokens or 0 
                    for detail in results.detailed_results
                )
            }
            
            # Write to file
            FileUtils.save_json_file(results_dict, final_path)
            
            self.logger.info(f"Successfully wrote results to: {final_path}")
            return str(final_path)
            
        except Exception as e:
            raise FileSystemError(f"Error writing results: {str(e)}")
    
    def _get_default_output_path(self, paper_id: str) -> Path:
        """Get default output path for a paper."""
        return Path(self.data_dir) / Constants.EVALUATIONS_DIR / f"{paper_id}_evaluation{Constants.JSON_EXT}"


class ConsoleResultsWriter(ResultsWriter, LoggerMixin):
    """Console-based results writer for displaying results."""
    
    def __init__(self, verbose: bool = False):
        """Initialize console results writer."""
        self.verbose = verbose
        self.logger.info("Initialized console results writer")
    
    def write_results(self, results: EvaluationResult, output_path: Optional[str] = None) -> str:
        """Write evaluation results to console."""
        try:
            self.logger.debug(f"Writing results to console for paper: {results.paper_id}")
            
            # Print main results
            print(f"=== Evaluation Results for Paper {results.paper_id} ===")
            print(f"TP: {results.TP}, FP: {results.FP}, FN: {results.FN}")
            print(f"Precision: {results.precision:.3f}")
            print(f"Recall: {results.recall:.3f}")
            print(f"F1: {results.f1:.3f}")
            
            # Show reasoning token usage
            total_reasoning_tokens = sum(
                detail.llm_response.reasoning_tokens or 0 
                for detail in results.detailed_results
            )
            print(f"Total reasoning tokens used: {total_reasoning_tokens}")
            
            # Show detailed results if verbose
            if self.verbose:
                print("\n=== Detailed Results ===")
                for i, detail in enumerate(results.detailed_results):
                    summary = (
                        f"\nExtraction {i + 1}: {detail.classification}\n"
                        f"  Confidence: {detail.llm_response.confidence}\n"
                        f"  Reason: {detail.llm_response.reason}\n"
                    )
                    if detail.llm_response.reasoning:
                        summary += f"  Reasoning: {detail.llm_response.reasoning}\n"
                    
                    print(summary.encode('utf-8').decode(sys.stdout.encoding, errors='ignore'))
                
                if results.unmatched_gt_items:
                    print("\n=== Unmatched Ground Truth Items ({len(results.unmatched_gt_items)}) ===")
                    for item in results.unmatched_gt_items:
                        summary = f"  - {item.get('adc_name', 'Unknown')} / {item.get('model_name', 'Unknown')} / {item.get('endpoint_name', 'Unknown')}"
                        print(summary.encode('utf-8').decode(sys.stdout.encoding, errors='ignore'))
            
            return "console"
            
        except Exception as e:
            raise FileSystemError(f"Error writing results to console: {str(e)}")


class MultiResultsWriter(ResultsWriter, LoggerMixin):
    """Multi-format results writer that can write to multiple destinations."""
    
    def __init__(self, writers: Optional[list[ResultsWriter]] = None):
        """Initialize multi-format results writer."""
        self.writers = writers or [JSONResultsWriter(), ConsoleResultsWriter()]
        self.logger.info(f"Initialized multi-format results writer with {len(self.writers)} writers")
    
    def write_results(self, results: EvaluationResult, output_path: Optional[str] = None) -> str:
        """Write evaluation results using all configured writers."""
        try:
            output_paths = []
            
            for writer in self.writers:
                try:
                    path = writer.write_results(results, output_path)
                    output_paths.append(path)
                except Exception as e:
                    self.logger.error(f"Writer {type(writer).__name__} failed: {str(e)}")
                    # Continue with other writers
            
            if not output_paths:
                raise FileSystemError("All writers failed")
            
            return "; ".join(output_paths)
            
        except Exception as e:
            raise FileSystemError(f"Error writing results with multi-writer: {str(e)}")
    
    def add_writer(self, writer: ResultsWriter) -> None:
        """Add a new writer to the multi-writer."""
        self.writers.append(writer)
        self.logger.info(f"Added writer: {type(writer).__name__}")
    
    def remove_writer(self, writer_type: type) -> None:
        """Remove a writer by type."""
        self.writers = [w for w in self.writers if not isinstance(w, writer_type)]
        self.logger.info(f"Removed writer: {writer_type.__name__}")