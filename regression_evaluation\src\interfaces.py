"""
Abstract interfaces for the ADC evaluation system.

This module defines the core interfaces that enable dependency injection
and make the system more testable and extensible.
"""

from abc import ABC, abstractmethod
from typing import List, Optional, Dict, Any
from models import (
    ExtractionRow, 
    GroundTruthDocument, 
    LLMMatchResponse, 
    EvaluationResult,
    ExtractedEndpoint,
    GTEndpoint
)


class LLMClient(ABC):
    """Abstract interface for LLM clients."""
    
    @abstractmethod
    def generate_structured_response(
        self, 
        system_prompt: str, 
        user_prompt: str, 
        response_model: type,
        max_tokens: int = 2000
    ) -> Any:
        """Generate structured response using the LLM."""
        pass
    
    @abstractmethod
    def generate_chat_response(
        self, 
        system_prompt: str, 
        user_prompt: str, 
        response_model: type,
        max_tokens: int = 1500
    ) -> Any:
        """Generate chat response as fallback."""
        pass


class Matcher(ABC):
    """Abstract interface for matching extracted data against ground truth."""
    
    @abstractmethod
    def match_endpoint(
        self, 
        extracted_endpoint: ExtractedEndpoint, 
        gt_endpoints: List[GTEndpoint]
    ) -> LLMMatchResponse:
        """Match an extracted endpoint against ground truth endpoints."""
        pass


class Evaluator(ABC):
    """Abstract interface for evaluation systems."""
    
    @abstractmethod
    def evaluate(
        self, 
        extraction_results: List[ExtractionRow], 
        ground_truth: GroundTruthDocument,
        treat_additional_correct_as_incorrect: bool = False
    ) -> EvaluationResult:
        """Evaluate extraction results against ground truth."""
        pass


class DataLoader(ABC):
    """Abstract interface for data loading operations."""
    
    @abstractmethod
    def load_ground_truth(self, paper_id: str) -> GroundTruthDocument:
        """Load ground truth data for a paper."""
        pass
    
    @abstractmethod
    def load_extraction_results(self, paper_id: str) -> List[ExtractionRow]:
        """Load extraction results for a paper."""
        pass


class DataValidator(ABC):
    """Abstract interface for data validation."""
    
    @abstractmethod
    def validate_ground_truth(self, gt_doc: GroundTruthDocument) -> Dict[str, Any]:
        """Validate ground truth document."""
        pass
    
    @abstractmethod
    def validate_extraction_results(self, results: List[ExtractionRow]) -> Dict[str, Any]:
        """Validate extraction results."""
        pass


class ResultsWriter(ABC):
    """Abstract interface for writing evaluation results."""
    
    @abstractmethod
    def write_results(self, results: EvaluationResult, output_path: str) -> None:
        """Write evaluation results to file."""
        pass