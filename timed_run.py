import subprocess
import time

# Command to run
command = [
    'python',
    'src/extraction_pipeline_parallel_strurcture.py',
    '--input-file',
    'output/w4226170358.md'
]

run_times = []
num_runs = 5

for i in range(num_runs):
    start = time.time()
    subprocess.run(command, check=True)
    end = time.time()
    elapsed = end - start
    run_times.append(elapsed)
    print(f"Run {i+1}: {elapsed:.2f} seconds")

avg_time = sum(run_times) / num_runs
print(f"Average time: {avg_time:.2f} seconds")