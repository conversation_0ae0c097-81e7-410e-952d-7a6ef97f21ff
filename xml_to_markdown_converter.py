#!/usr/bin/env python3
"""
Scientific Article XML to Markdown Converter

This script converts scientific article XML (like JATS format) to a properly formatted Markdown file.
It handles journal article structures and creates readable markdown output.
"""

import xml.etree.ElementTree as ET
import re
import os


class ScientificXMLToMarkdownConverter:
    """Converts scientific article XML content to Markdown format."""

    def __init__(self):
        self.markdown_content = []
        self.references = {}
        self.ref_counter = 1

    def clean_text(self, text: str) -> str:
        """Clean and normalize text content"""
        if not text:
            return ""
        # Replace XML entities
        text = text.replace("&amp;", "&")
        text = text.replace("&lt;", "<")
        text = text.replace("&gt;", ">")
        text = text.replace("&quot;", '"')
        text = text.replace("&apos;", "'")
        # Normalize whitespace
        text = re.sub(r'\s+', ' ', text.strip())
        return text

    def extract_text_content(self, element) -> str:
        """Extract all text content from an element and its children"""
        if element is None:
            return ""

        text_parts = []
        if element.text:
            text_parts.append(element.text)

        for child in element:
            # Handle special formatting elements
            if child.tag == 'italic':
                text_parts.append(f"*{self.extract_text_content(child)}*")
            elif child.tag == 'bold':
                text_parts.append(f"**{self.extract_text_content(child)}**")
            elif child.tag == 'sup':
                text_parts.append(f"^{self.extract_text_content(child)}^")
            elif child.tag == 'sub':
                text_parts.append(f"_{self.extract_text_content(child)}_")
            elif child.tag == 'xref':
                # Handle cross-references
                ref_text = self.extract_text_content(child)
                if ref_text:
                    text_parts.append(f"[{ref_text}]")
            else:
                text_parts.append(self.extract_text_content(child))

            if child.tail:
                text_parts.append(child.tail)

        return self.clean_text(''.join(text_parts))

    def convert_article_meta(self, article_meta) -> None:
        """Convert article metadata to Markdown"""
        self.markdown_content.append("# Article Metadata\n")

        # Title
        title_group = article_meta.find('.//title-group')
        if title_group is not None:
            article_title = title_group.find('article-title')
            if article_title is not None:
                title = self.extract_text_content(article_title)
                self.markdown_content.append(f"## {title}\n")

        # Authors
        contrib_groups = article_meta.findall('.//contrib-group')
        authors = []
        for group in contrib_groups:
            for contrib in group.findall('contrib[@contrib-type="author"]'):
                name_elem = contrib.find('.//surname')
                given_elem = contrib.find('.//given-names')
                if name_elem is not None and given_elem is not None:
                    surname = self.extract_text_content(name_elem)
                    given = self.extract_text_content(given_elem)
                    authors.append(f"{given} {surname}")

        if authors:
            self.markdown_content.append("### Authors\n")
            for author in authors:
                self.markdown_content.append(f"- {author}\n")
            self.markdown_content.append("\n")

        # Journal info
        journal_title = article_meta.find('.//journal-title')
        if journal_title is not None:
            journal = self.extract_text_content(journal_title)
            self.markdown_content.append(f"**Journal:** {journal}\n\n")

        # Publication date
        pub_date = article_meta.find('.//pub-date[@pub-type="epub"]')
        if pub_date is not None:
            year = pub_date.find('year')
            month = pub_date.find('month')
            day = pub_date.find('day')
            if year is not None:
                date_str = self.extract_text_content(year)
                if month is not None:
                    date_str = f"{self.extract_text_content(month)}/{date_str}"
                if day is not None:
                    date_str = f"{self.extract_text_content(day)}/{date_str}"
                self.markdown_content.append(f"**Publication Date:** {date_str}\n\n")

        # Abstract
        abstract = article_meta.find('.//abstract')
        if abstract is not None:
            self.markdown_content.append("## Abstract\n\n")
            for p in abstract.findall('.//p'):
                content = self.extract_text_content(p)
                if content:
                    self.markdown_content.append(f"{content}\n\n")

        # Keywords
        kwd_group = article_meta.find('.//kwd-group')
        if kwd_group is not None:
            keywords = []
            for kwd in kwd_group.findall('kwd'):
                keyword = self.extract_text_content(kwd)
                if keyword:
                    keywords.append(keyword)
            if keywords:
                self.markdown_content.append("**Keywords:** " + ", ".join(keywords) + "\n\n")

    def convert_body_sections(self, body) -> None:
        """Convert body sections to Markdown"""
        self.markdown_content.append("---\n\n")

        for sec in body.findall('.//sec'):
            # Section title
            title_elem = sec.find('title')
            if title_elem is not None:
                title = self.extract_text_content(title_elem)
                self.markdown_content.append(f"## {title}\n\n")

            # Section content
            for elem in sec:
                if elem.tag == 'p':
                    content = self.extract_text_content(elem)
                    if content:
                        self.markdown_content.append(f"{content}\n\n")
                elif elem.tag == 'list':
                    self.convert_list(elem)
                elif elem.tag == 'table-wrap':
                    self.convert_table(elem)
                elif elem.tag == 'fig':
                    self.convert_figure(elem)

    def convert_list(self, list_elem) -> None:
        """Convert XML list to Markdown list"""
        list_type = list_elem.get('list-type', 'bullet')

        for i, item in enumerate(list_elem.findall('list-item'), 1):
            content = ""
            for p in item.findall('p'):
                p_content = self.extract_text_content(p)
                if p_content:
                    content += p_content + " "

            if content:
                if list_type in ['roman-lower', 'alpha-lower', 'order']:
                    self.markdown_content.append(f"{i}. {content.strip()}\n")
                else:
                    self.markdown_content.append(f"- {content.strip()}\n")

        self.markdown_content.append("\n")

    def convert_table(self, table_wrap) -> None:
        """Convert XML table to Markdown table"""
        label = table_wrap.find('label')
        caption = table_wrap.find('caption')

        if label is not None:
            label_text = self.extract_text_content(label)
            self.markdown_content.append(f"### {label_text}\n\n")

        if caption is not None:
            caption_text = self.extract_text_content(caption)
            self.markdown_content.append(f"*{caption_text}*\n\n")

        table = table_wrap.find('.//table')
        if table is not None:
            # Process table headers
            thead = table.find('thead')
            headers = []
            if thead is not None:
                header_row = thead.find('tr')
                if header_row is not None:
                    for th in header_row.findall('th'):
                        header_text = self.extract_text_content(th)
                        headers.append(header_text if header_text else " ")

            # Process table body
            tbody = table.find('tbody')
            rows = []
            if tbody is not None:
                for tr in tbody.findall('tr'):
                    cells = []
                    for td in tr.findall('td'):
                        cell_text = self.extract_text_content(td)

                        # Clean cell text and handle empty cells
                        if cell_text.strip():
                            cells.append(cell_text.strip())
                        else:
                            cells.append(" ")

                    if cells:
                        rows.append(cells)

            # Create markdown table
            if headers and rows:
                # Ensure all rows have the same number of columns as headers
                max_cols = len(headers)

                self.markdown_content.append("| " + " | ".join(headers) + " |\n")
                self.markdown_content.append("| " + " | ".join(["---"] * max_cols) + " |\n")

                for row in rows:
                    # Pad row to match header length
                    while len(row) < max_cols:
                        row.append(" ")
                    # Truncate if too long
                    row = row[:max_cols]
                    self.markdown_content.append("| " + " | ".join(row) + " |\n")
            elif rows:
                # Table without headers - create simple table
                if rows:
                    max_cols = max(len(row) for row in rows)
                    # Create generic headers
                    headers = [f"Column {i+1}" for i in range(max_cols)]

                    self.markdown_content.append("| " + " | ".join(headers) + " |\n")
                    self.markdown_content.append("| " + " | ".join(["---"] * max_cols) + " |\n")

                    for row in rows:
                        # Pad row to match max columns
                        while len(row) < max_cols:
                            row.append(" ")
                        self.markdown_content.append("| " + " | ".join(row) + " |\n")

        self.markdown_content.append("\n")

    def convert_figure(self, fig_elem) -> None:
        """Convert XML figure to Markdown"""
        label = fig_elem.find('label')
        caption = fig_elem.find('caption')

        if label is not None:
            label_text = self.extract_text_content(label)
            self.markdown_content.append(f"### {label_text}\n\n")

        if caption is not None:
            caption_text = self.extract_text_content(caption)
            self.markdown_content.append(f"*{caption_text}*\n\n")

        # Note about graphic
        graphic = fig_elem.find('graphic')
        if graphic is not None:
            href = graphic.get('{http://www.w3.org/1999/xlink}href')
            if href:
                self.markdown_content.append(f"![Figure]({href})\n\n")
            else:
                self.markdown_content.append("*[Figure graphic not available]*\n\n")

    def convert_references(self, back) -> None:
        """Convert references section to Markdown"""
        ref_list = back.find('.//ref-list')
        if ref_list is not None:
            self.markdown_content.append("## References\n\n")

            for ref in ref_list.findall('ref'):
                label = ref.find('label')
                mixed_citation = ref.find('.//mixed-citation')

                if label is not None and mixed_citation is not None:
                    label_text = self.extract_text_content(label)
                    citation_text = self.extract_text_content(mixed_citation)
                    self.markdown_content.append(f"{label_text}. {citation_text}\n\n")

    def convert_xml_to_markdown(self, xml_file_path: str) -> str:
        """Main conversion method"""
        try:
            tree = ET.parse(xml_file_path)
            root = tree.getroot()

            # Reset content
            self.markdown_content = []

            # Convert front matter (metadata)
            front = root.find('front')
            if front is not None:
                article_meta = front.find('.//article-meta')
                if article_meta is not None:
                    self.convert_article_meta(article_meta)

            # Convert body content
            body = root.find('body')
            if body is not None:
                self.convert_body_sections(body)

            # Convert back matter (references, etc.)
            back = root.find('back')
            if back is not None:
                self.convert_references(back)

            return ''.join(self.markdown_content)

        except ET.ParseError as e:
            return f"# XML Parse Error\n\nError parsing XML file: {e}\n"
        except FileNotFoundError:
            return f"# File Not Found\n\nThe file '{xml_file_path}' was not found.\n"
        except Exception as e:
            return f"# Conversion Error\n\nError converting XML to Markdown: {e}\n"


def main():
    """Main function to convert XML to Markdown."""
    # File paths - use the copied file in workspace
    input_file = "review.xml"
    output_file = "review.md"

    # Check if input file exists
    if not os.path.exists(input_file):
        print(f"Error: Input file '{input_file}' not found.")
        print("Please make sure the file exists and the path is correct.")
        return

    # Create converter instance
    converter = ScientificXMLToMarkdownConverter()

    # Convert XML to Markdown
    print(f"Converting '{input_file}' to Markdown...")
    markdown_content = converter.convert_xml_to_markdown(input_file)

    # Write to output file
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(markdown_content)

        print(f"✅ Successfully converted XML to Markdown!")
        print(f"📄 Output saved to: {output_file}")
        print(f"📊 Generated {len(markdown_content.splitlines())} lines of markdown")

        # Show preview of first few lines
        lines = markdown_content.splitlines()
        print("\n📋 Preview (first 10 lines):")
        print("-" * 50)
        for i, line in enumerate(lines[:10], 1):
            print(f"{i:2d}: {line}")
        if len(lines) > 10:
            print(f"... and {len(lines) - 10} more lines")

    except Exception as e:
        print(f"❌ Error writing output file: {e}")


if __name__ == "__main__":
    main()
